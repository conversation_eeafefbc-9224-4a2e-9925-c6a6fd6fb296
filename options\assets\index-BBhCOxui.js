(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const N of document.querySelectorAll('link[rel="modulepreload"]'))y(N);new MutationObserver(N=>{for(const k of N)if(k.type==="childList")for(const E of k.addedNodes)E.tagName==="LINK"&&E.rel==="modulepreload"&&y(E)}).observe(document,{childList:!0,subtree:!0});function i(N){const k={};return N.integrity&&(k.integrity=N.integrity),N.referrerPolicy&&(k.referrerPolicy=N.referrerPolicy),N.crossOrigin==="use-credentials"?k.credentials="include":N.crossOrigin==="anonymous"?k.credentials="omit":k.credentials="same-origin",k}function y(N){if(N.ep)return;N.ep=!0;const k=i(N);fetch(N.href,k)}})();var wi={exports:{}},tl={},Si={exports:{}},le={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yc;function rp(){if(yc)return le;yc=1;var s=Symbol.for("react.element"),u=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),k=Symbol.for("react.provider"),E=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),T=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),X=Symbol.iterator;function Y(g){return g===null||typeof g!="object"?null:(g=X&&g[X]||g["@@iterator"],typeof g=="function"?g:null)}var ue={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ce=Object.assign,R={};function H(g,b,te){this.props=g,this.context=b,this.refs=R,this.updater=te||ue}H.prototype.isReactComponent={},H.prototype.setState=function(g,b){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,b,"setState")},H.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function re(){}re.prototype=H.prototype;function ge(g,b,te){this.props=g,this.context=b,this.refs=R,this.updater=te||ue}var Ce=ge.prototype=new re;Ce.constructor=ge,ce(Ce,H.prototype),Ce.isPureReactComponent=!0;var me=Array.isArray,Te=Object.prototype.hasOwnProperty,Z={current:null},ye={key:!0,ref:!0,__self:!0,__source:!0};function He(g,b,te){var ee,se={},ie=null,we=null;if(b!=null)for(ee in b.ref!==void 0&&(we=b.ref),b.key!==void 0&&(ie=""+b.key),b)Te.call(b,ee)&&!ye.hasOwnProperty(ee)&&(se[ee]=b[ee]);var he=arguments.length-2;if(he===1)se.children=te;else if(1<he){for(var Se=Array(he),et=0;et<he;et++)Se[et]=arguments[et+2];se.children=Se}if(g&&g.defaultProps)for(ee in he=g.defaultProps,he)se[ee]===void 0&&(se[ee]=he[ee]);return{$$typeof:s,type:g,key:ie,ref:we,props:se,_owner:Z.current}}function Ze(g,b){return{$$typeof:s,type:g.type,key:b,ref:g.ref,props:g.props,_owner:g._owner}}function Me(g){return typeof g=="object"&&g!==null&&g.$$typeof===s}function rt(g){var b={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(te){return b[te]})}var oe=/\/+/g;function Ve(g,b){return typeof g=="object"&&g!==null&&g.key!=null?rt(""+g.key):b.toString(36)}function Le(g,b,te,ee,se){var ie=typeof g;(ie==="undefined"||ie==="boolean")&&(g=null);var we=!1;if(g===null)we=!0;else switch(ie){case"string":case"number":we=!0;break;case"object":switch(g.$$typeof){case s:case u:we=!0}}if(we)return we=g,se=se(we),g=ee===""?"."+Ve(we,0):ee,me(se)?(te="",g!=null&&(te=g.replace(oe,"$&/")+"/"),Le(se,b,te,"",function(et){return et})):se!=null&&(Me(se)&&(se=Ze(se,te+(!se.key||we&&we.key===se.key?"":(""+se.key).replace(oe,"$&/")+"/")+g)),b.push(se)),1;if(we=0,ee=ee===""?".":ee+":",me(g))for(var he=0;he<g.length;he++){ie=g[he];var Se=ee+Ve(ie,he);we+=Le(ie,b,te,Se,se)}else if(Se=Y(g),typeof Se=="function")for(g=Se.call(g),he=0;!(ie=g.next()).done;)ie=ie.value,Se=ee+Ve(ie,he++),we+=Le(ie,b,te,Se,se);else if(ie==="object")throw b=String(g),Error("Objects are not valid as a React child (found: "+(b==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":b)+"). If you meant to render a collection of children, use an array instead.");return we}function Je(g,b,te){if(g==null)return g;var ee=[],se=0;return Le(g,ee,"","",function(ie){return b.call(te,ie,se++)}),ee}function Oe(g){if(g._status===-1){var b=g._result;b=b(),b.then(function(te){(g._status===0||g._status===-1)&&(g._status=1,g._result=te)},function(te){(g._status===0||g._status===-1)&&(g._status=2,g._result=te)}),g._status===-1&&(g._status=0,g._result=b)}if(g._status===1)return g._result.default;throw g._result}var xe={current:null},L={transition:null},K={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:L,ReactCurrentOwner:Z};function I(){throw Error("act(...) is not supported in production builds of React.")}return le.Children={map:Je,forEach:function(g,b,te){Je(g,function(){b.apply(this,arguments)},te)},count:function(g){var b=0;return Je(g,function(){b++}),b},toArray:function(g){return Je(g,function(b){return b})||[]},only:function(g){if(!Me(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},le.Component=H,le.Fragment=i,le.Profiler=N,le.PureComponent=ge,le.StrictMode=y,le.Suspense=_,le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=K,le.act=I,le.cloneElement=function(g,b,te){if(g==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+g+".");var ee=ce({},g.props),se=g.key,ie=g.ref,we=g._owner;if(b!=null){if(b.ref!==void 0&&(ie=b.ref,we=Z.current),b.key!==void 0&&(se=""+b.key),g.type&&g.type.defaultProps)var he=g.type.defaultProps;for(Se in b)Te.call(b,Se)&&!ye.hasOwnProperty(Se)&&(ee[Se]=b[Se]===void 0&&he!==void 0?he[Se]:b[Se])}var Se=arguments.length-2;if(Se===1)ee.children=te;else if(1<Se){he=Array(Se);for(var et=0;et<Se;et++)he[et]=arguments[et+2];ee.children=he}return{$$typeof:s,type:g.type,key:se,ref:ie,props:ee,_owner:we}},le.createContext=function(g){return g={$$typeof:E,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},g.Provider={$$typeof:k,_context:g},g.Consumer=g},le.createElement=He,le.createFactory=function(g){var b=He.bind(null,g);return b.type=g,b},le.createRef=function(){return{current:null}},le.forwardRef=function(g){return{$$typeof:P,render:g}},le.isValidElement=Me,le.lazy=function(g){return{$$typeof:J,_payload:{_status:-1,_result:g},_init:Oe}},le.memo=function(g,b){return{$$typeof:T,type:g,compare:b===void 0?null:b}},le.startTransition=function(g){var b=L.transition;L.transition={};try{g()}finally{L.transition=b}},le.unstable_act=I,le.useCallback=function(g,b){return xe.current.useCallback(g,b)},le.useContext=function(g){return xe.current.useContext(g)},le.useDebugValue=function(){},le.useDeferredValue=function(g){return xe.current.useDeferredValue(g)},le.useEffect=function(g,b){return xe.current.useEffect(g,b)},le.useId=function(){return xe.current.useId()},le.useImperativeHandle=function(g,b,te){return xe.current.useImperativeHandle(g,b,te)},le.useInsertionEffect=function(g,b){return xe.current.useInsertionEffect(g,b)},le.useLayoutEffect=function(g,b){return xe.current.useLayoutEffect(g,b)},le.useMemo=function(g,b){return xe.current.useMemo(g,b)},le.useReducer=function(g,b,te){return xe.current.useReducer(g,b,te)},le.useRef=function(g){return xe.current.useRef(g)},le.useState=function(g){return xe.current.useState(g)},le.useSyncExternalStore=function(g,b,te){return xe.current.useSyncExternalStore(g,b,te)},le.useTransition=function(){return xe.current.useTransition()},le.version="18.3.1",le}var vc;function zi(){return vc||(vc=1,Si.exports=rp()),Si.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xc;function lp(){if(xc)return tl;xc=1;var s=zi(),u=Symbol.for("react.element"),i=Symbol.for("react.fragment"),y=Object.prototype.hasOwnProperty,N=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,k={key:!0,ref:!0,__self:!0,__source:!0};function E(P,_,T){var J,X={},Y=null,ue=null;T!==void 0&&(Y=""+T),_.key!==void 0&&(Y=""+_.key),_.ref!==void 0&&(ue=_.ref);for(J in _)y.call(_,J)&&!k.hasOwnProperty(J)&&(X[J]=_[J]);if(P&&P.defaultProps)for(J in _=P.defaultProps,_)X[J]===void 0&&(X[J]=_[J]);return{$$typeof:u,type:P,key:Y,ref:ue,props:X,_owner:N.current}}return tl.Fragment=i,tl.jsx=E,tl.jsxs=E,tl}var wc;function op(){return wc||(wc=1,wi.exports=lp()),wi.exports}var d=op(),ko={},Ni={exports:{}},ut={},ki={exports:{}},Pi={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sc;function sp(){return Sc||(Sc=1,function(s){function u(L,K){var I=L.length;L.push(K);e:for(;0<I;){var g=I-1>>>1,b=L[g];if(0<N(b,K))L[g]=K,L[I]=b,I=g;else break e}}function i(L){return L.length===0?null:L[0]}function y(L){if(L.length===0)return null;var K=L[0],I=L.pop();if(I!==K){L[0]=I;e:for(var g=0,b=L.length,te=b>>>1;g<te;){var ee=2*(g+1)-1,se=L[ee],ie=ee+1,we=L[ie];if(0>N(se,I))ie<b&&0>N(we,se)?(L[g]=we,L[ie]=I,g=ie):(L[g]=se,L[ee]=I,g=ee);else if(ie<b&&0>N(we,I))L[g]=we,L[ie]=I,g=ie;else break e}}return K}function N(L,K){var I=L.sortIndex-K.sortIndex;return I!==0?I:L.id-K.id}if(typeof performance=="object"&&typeof performance.now=="function"){var k=performance;s.unstable_now=function(){return k.now()}}else{var E=Date,P=E.now();s.unstable_now=function(){return E.now()-P}}var _=[],T=[],J=1,X=null,Y=3,ue=!1,ce=!1,R=!1,H=typeof setTimeout=="function"?setTimeout:null,re=typeof clearTimeout=="function"?clearTimeout:null,ge=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function Ce(L){for(var K=i(T);K!==null;){if(K.callback===null)y(T);else if(K.startTime<=L)y(T),K.sortIndex=K.expirationTime,u(_,K);else break;K=i(T)}}function me(L){if(R=!1,Ce(L),!ce)if(i(_)!==null)ce=!0,Oe(Te);else{var K=i(T);K!==null&&xe(me,K.startTime-L)}}function Te(L,K){ce=!1,R&&(R=!1,re(He),He=-1),ue=!0;var I=Y;try{for(Ce(K),X=i(_);X!==null&&(!(X.expirationTime>K)||L&&!rt());){var g=X.callback;if(typeof g=="function"){X.callback=null,Y=X.priorityLevel;var b=g(X.expirationTime<=K);K=s.unstable_now(),typeof b=="function"?X.callback=b:X===i(_)&&y(_),Ce(K)}else y(_);X=i(_)}if(X!==null)var te=!0;else{var ee=i(T);ee!==null&&xe(me,ee.startTime-K),te=!1}return te}finally{X=null,Y=I,ue=!1}}var Z=!1,ye=null,He=-1,Ze=5,Me=-1;function rt(){return!(s.unstable_now()-Me<Ze)}function oe(){if(ye!==null){var L=s.unstable_now();Me=L;var K=!0;try{K=ye(!0,L)}finally{K?Ve():(Z=!1,ye=null)}}else Z=!1}var Ve;if(typeof ge=="function")Ve=function(){ge(oe)};else if(typeof MessageChannel<"u"){var Le=new MessageChannel,Je=Le.port2;Le.port1.onmessage=oe,Ve=function(){Je.postMessage(null)}}else Ve=function(){H(oe,0)};function Oe(L){ye=L,Z||(Z=!0,Ve())}function xe(L,K){He=H(function(){L(s.unstable_now())},K)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(L){L.callback=null},s.unstable_continueExecution=function(){ce||ue||(ce=!0,Oe(Te))},s.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ze=0<L?Math.floor(1e3/L):5},s.unstable_getCurrentPriorityLevel=function(){return Y},s.unstable_getFirstCallbackNode=function(){return i(_)},s.unstable_next=function(L){switch(Y){case 1:case 2:case 3:var K=3;break;default:K=Y}var I=Y;Y=K;try{return L()}finally{Y=I}},s.unstable_pauseExecution=function(){},s.unstable_requestPaint=function(){},s.unstable_runWithPriority=function(L,K){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var I=Y;Y=L;try{return K()}finally{Y=I}},s.unstable_scheduleCallback=function(L,K,I){var g=s.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?g+I:g):I=g,L){case 1:var b=-1;break;case 2:b=250;break;case 5:b=**********;break;case 4:b=1e4;break;default:b=5e3}return b=I+b,L={id:J++,callback:K,priorityLevel:L,startTime:I,expirationTime:b,sortIndex:-1},I>g?(L.sortIndex=I,u(T,L),i(_)===null&&L===i(T)&&(R?(re(He),He=-1):R=!0,xe(me,I-g))):(L.sortIndex=b,u(_,L),ce||ue||(ce=!0,Oe(Te))),L},s.unstable_shouldYield=rt,s.unstable_wrapCallback=function(L){var K=Y;return function(){var I=Y;Y=K;try{return L.apply(this,arguments)}finally{Y=I}}}}(Pi)),Pi}var Nc;function ip(){return Nc||(Nc=1,ki.exports=sp()),ki.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kc;function ap(){if(kc)return ut;kc=1;var s=zi(),u=ip();function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var y=new Set,N={};function k(e,t){E(e,t),E(e+"Capture",t)}function E(e,t){for(N[e]=t,e=0;e<t.length;e++)y.add(t[e])}var P=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_=Object.prototype.hasOwnProperty,T=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,J={},X={};function Y(e){return _.call(X,e)?!0:_.call(J,e)?!1:T.test(e)?X[e]=!0:(J[e]=!0,!1)}function ue(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ce(e,t,n,r){if(t===null||typeof t>"u"||ue(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function R(e,t,n,r,l,o,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var H={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){H[e]=new R(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];H[t]=new R(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){H[e]=new R(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){H[e]=new R(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){H[e]=new R(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){H[e]=new R(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){H[e]=new R(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){H[e]=new R(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){H[e]=new R(e,5,!1,e.toLowerCase(),null,!1,!1)});var re=/[\-:]([a-z])/g;function ge(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(re,ge);H[t]=new R(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(re,ge);H[t]=new R(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(re,ge);H[t]=new R(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){H[e]=new R(e,1,!1,e.toLowerCase(),null,!1,!1)}),H.xlinkHref=new R("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){H[e]=new R(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ce(e,t,n,r){var l=H.hasOwnProperty(t)?H[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ce(t,n,l,r)&&(n=null),r||l===null?Y(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var me=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Te=Symbol.for("react.element"),Z=Symbol.for("react.portal"),ye=Symbol.for("react.fragment"),He=Symbol.for("react.strict_mode"),Ze=Symbol.for("react.profiler"),Me=Symbol.for("react.provider"),rt=Symbol.for("react.context"),oe=Symbol.for("react.forward_ref"),Ve=Symbol.for("react.suspense"),Le=Symbol.for("react.suspense_list"),Je=Symbol.for("react.memo"),Oe=Symbol.for("react.lazy"),xe=Symbol.for("react.offscreen"),L=Symbol.iterator;function K(e){return e===null||typeof e!="object"?null:(e=L&&e[L]||e["@@iterator"],typeof e=="function"?e:null)}var I=Object.assign,g;function b(e){if(g===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);g=t&&t[1]||""}return`
`+g+e}var te=!1;function ee(e,t){if(!e||te)return"";te=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(S){var r=S}Reflect.construct(e,[],t)}else{try{t.call()}catch(S){r=S}e.call(t.prototype)}else{try{throw Error()}catch(S){r=S}e()}}catch(S){if(S&&r&&typeof S.stack=="string"){for(var l=S.stack.split(`
`),o=r.stack.split(`
`),a=l.length-1,f=o.length-1;1<=a&&0<=f&&l[a]!==o[f];)f--;for(;1<=a&&0<=f;a--,f--)if(l[a]!==o[f]){if(a!==1||f!==1)do if(a--,f--,0>f||l[a]!==o[f]){var p=`
`+l[a].replace(" at new "," at ");return e.displayName&&p.includes("<anonymous>")&&(p=p.replace("<anonymous>",e.displayName)),p}while(1<=a&&0<=f);break}}}finally{te=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?b(e):""}function se(e){switch(e.tag){case 5:return b(e.type);case 16:return b("Lazy");case 13:return b("Suspense");case 19:return b("SuspenseList");case 0:case 2:case 15:return e=ee(e.type,!1),e;case 11:return e=ee(e.type.render,!1),e;case 1:return e=ee(e.type,!0),e;default:return""}}function ie(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ye:return"Fragment";case Z:return"Portal";case Ze:return"Profiler";case He:return"StrictMode";case Ve:return"Suspense";case Le:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rt:return(e.displayName||"Context")+".Consumer";case Me:return(e._context.displayName||"Context")+".Provider";case oe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Je:return t=e.displayName||null,t!==null?t:ie(e.type)||"Memo";case Oe:t=e._payload,e=e._init;try{return ie(e(t))}catch{}}return null}function we(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ie(t);case 8:return t===He?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function he(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Se(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function et(e){var t=Se(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(a){r=""+a,o.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wn(e){e._valueTracker||(e._valueTracker=et(e))}function mr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Se(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Fn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function hr(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function fl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=he(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function pl(e,t){t=t.checked,t!=null&&Ce(e,"checked",t,!1)}function gr(e,t){pl(e,t);var n=he(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?v(e,t.type,n):t.hasOwnProperty("defaultValue")&&v(e,t.type,he(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function c(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function v(e,t,n){(t!=="number"||Fn(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var m=Array.isArray;function $(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+he(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function M(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(i(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function F(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(i(92));if(m(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:he(n)}}function de(e,t){var n=he(t.value),r=he(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ct(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function St(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function In(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?St(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Sn,yr=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Sn=Sn||document.createElement("div"),Sn.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Sn.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Gt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Yt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ml=["Webkit","ms","Moz","O"];Object.keys(Yt).forEach(function(e){ml.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Yt[t]=Yt[e]})});function vr(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Yt.hasOwnProperty(e)&&Yt[e]?(""+t).trim():t+"px"}function Ai(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=vr(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var id=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function $o(e,t){if(t){if(id[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(i(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(t.style!=null&&typeof t.style!="object")throw Error(i(62))}}function Ao(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var To=null;function Lo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ro=null,Un=null,Mn=null;function Ti(e){if(e=Mr(e)){if(typeof Ro!="function")throw Error(i(280));var t=e.stateNode;t&&(t=Fl(t),Ro(e.stateNode,e.type,t))}}function Li(e){Un?Mn?Mn.push(e):Mn=[e]:Un=e}function Ri(){if(Un){var e=Un,t=Mn;if(Mn=Un=null,Ti(e),t)for(e=0;e<t.length;e++)Ti(t[e])}}function Fi(e,t){return e(t)}function Ii(){}var Fo=!1;function Ui(e,t,n){if(Fo)return e(t,n);Fo=!0;try{return Fi(e,t,n)}finally{Fo=!1,(Un!==null||Mn!==null)&&(Ii(),Ri())}}function xr(e,t){var n=e.stateNode;if(n===null)return null;var r=Fl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(i(231,t,typeof n));return n}var Io=!1;if(P)try{var wr={};Object.defineProperty(wr,"passive",{get:function(){Io=!0}}),window.addEventListener("test",wr,wr),window.removeEventListener("test",wr,wr)}catch{Io=!1}function ad(e,t,n,r,l,o,a,f,p){var S=Array.prototype.slice.call(arguments,3);try{t.apply(n,S)}catch(C){this.onError(C)}}var Sr=!1,hl=null,gl=!1,Uo=null,ud={onError:function(e){Sr=!0,hl=e}};function cd(e,t,n,r,l,o,a,f,p){Sr=!1,hl=null,ad.apply(ud,arguments)}function dd(e,t,n,r,l,o,a,f,p){if(cd.apply(this,arguments),Sr){if(Sr){var S=hl;Sr=!1,hl=null}else throw Error(i(198));gl||(gl=!0,Uo=S)}}function Nn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Mi(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Vi(e){if(Nn(e)!==e)throw Error(i(188))}function fd(e){var t=e.alternate;if(!t){if(t=Nn(e),t===null)throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return Vi(l),e;if(o===r)return Vi(l),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=l,r=o;else{for(var a=!1,f=l.child;f;){if(f===n){a=!0,n=l,r=o;break}if(f===r){a=!0,r=l,n=o;break}f=f.sibling}if(!a){for(f=o.child;f;){if(f===n){a=!0,n=o,r=l;break}if(f===r){a=!0,r=o,n=l;break}f=f.sibling}if(!a)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function Di(e){return e=fd(e),e!==null?Bi(e):null}function Bi(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bi(e);if(t!==null)return t;e=e.sibling}return null}var Wi=u.unstable_scheduleCallback,Hi=u.unstable_cancelCallback,pd=u.unstable_shouldYield,md=u.unstable_requestPaint,$e=u.unstable_now,hd=u.unstable_getCurrentPriorityLevel,Mo=u.unstable_ImmediatePriority,Ki=u.unstable_UserBlockingPriority,yl=u.unstable_NormalPriority,gd=u.unstable_LowPriority,Qi=u.unstable_IdlePriority,vl=null,Ot=null;function yd(e){if(Ot&&typeof Ot.onCommitFiberRoot=="function")try{Ot.onCommitFiberRoot(vl,e,void 0,(e.current.flags&128)===128)}catch{}}var Nt=Math.clz32?Math.clz32:wd,vd=Math.log,xd=Math.LN2;function wd(e){return e>>>=0,e===0?32:31-(vd(e)/xd|0)|0}var xl=64,wl=4194304;function Nr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Sl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,a=n&268435455;if(a!==0){var f=a&~l;f!==0?r=Nr(f):(o&=a,o!==0&&(r=Nr(o)))}else a=n&~l,a!==0?r=Nr(a):o!==0&&(r=Nr(o));if(r===0)return 0;if(t!==0&&t!==r&&(t&l)===0&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Nt(t),l=1<<n,r|=e[n],t&=~l;return r}function Sd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-Nt(o),f=1<<a,p=l[a];p===-1?((f&n)===0||(f&r)!==0)&&(l[a]=Sd(f,t)):p<=t&&(e.expiredLanes|=f),o&=~f}}function Vo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gi(){var e=xl;return xl<<=1,(xl&4194240)===0&&(xl=64),e}function Do(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function kr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Nt(t),e[t]=n}function kd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Nt(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Bo(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Nt(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var ve=0;function Yi(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Xi,Wo,qi,Zi,Ji,Ho=!1,Nl=[],Xt=null,qt=null,Zt=null,Pr=new Map,_r=new Map,Jt=[],Pd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ea(e,t){switch(e){case"focusin":case"focusout":Xt=null;break;case"dragenter":case"dragleave":qt=null;break;case"mouseover":case"mouseout":Zt=null;break;case"pointerover":case"pointerout":Pr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_r.delete(t.pointerId)}}function Er(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Mr(t),t!==null&&Wo(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function _d(e,t,n,r,l){switch(t){case"focusin":return Xt=Er(Xt,e,t,n,r,l),!0;case"dragenter":return qt=Er(qt,e,t,n,r,l),!0;case"mouseover":return Zt=Er(Zt,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Pr.set(o,Er(Pr.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,_r.set(o,Er(_r.get(o)||null,e,t,n,r,l)),!0}return!1}function ta(e){var t=kn(e.target);if(t!==null){var n=Nn(t);if(n!==null){if(t=n.tag,t===13){if(t=Mi(n),t!==null){e.blockedOn=t,Ji(e.priority,function(){qi(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function kl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);To=r,n.target.dispatchEvent(r),To=null}else return t=Mr(n),t!==null&&Wo(t),e.blockedOn=n,!1;t.shift()}return!0}function na(e,t,n){kl(e)&&n.delete(t)}function Ed(){Ho=!1,Xt!==null&&kl(Xt)&&(Xt=null),qt!==null&&kl(qt)&&(qt=null),Zt!==null&&kl(Zt)&&(Zt=null),Pr.forEach(na),_r.forEach(na)}function br(e,t){e.blockedOn===t&&(e.blockedOn=null,Ho||(Ho=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,Ed)))}function jr(e){function t(l){return br(l,e)}if(0<Nl.length){br(Nl[0],e);for(var n=1;n<Nl.length;n++){var r=Nl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Xt!==null&&br(Xt,e),qt!==null&&br(qt,e),Zt!==null&&br(Zt,e),Pr.forEach(t),_r.forEach(t),n=0;n<Jt.length;n++)r=Jt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Jt.length&&(n=Jt[0],n.blockedOn===null);)ta(n),n.blockedOn===null&&Jt.shift()}var Vn=me.ReactCurrentBatchConfig,Pl=!0;function bd(e,t,n,r){var l=ve,o=Vn.transition;Vn.transition=null;try{ve=1,Ko(e,t,n,r)}finally{ve=l,Vn.transition=o}}function jd(e,t,n,r){var l=ve,o=Vn.transition;Vn.transition=null;try{ve=4,Ko(e,t,n,r)}finally{ve=l,Vn.transition=o}}function Ko(e,t,n,r){if(Pl){var l=Qo(e,t,n,r);if(l===null)cs(e,t,r,_l,n),ea(e,r);else if(_d(l,e,t,n,r))r.stopPropagation();else if(ea(e,r),t&4&&-1<Pd.indexOf(e)){for(;l!==null;){var o=Mr(l);if(o!==null&&Xi(o),o=Qo(e,t,n,r),o===null&&cs(e,t,r,_l,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else cs(e,t,r,null,n)}}var _l=null;function Qo(e,t,n,r){if(_l=null,e=Lo(r),e=kn(e),e!==null)if(t=Nn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Mi(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return _l=e,null}function ra(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(hd()){case Mo:return 1;case Ki:return 4;case yl:case gd:return 16;case Qi:return 536870912;default:return 16}default:return 16}}var en=null,Go=null,El=null;function la(){if(El)return El;var e,t=Go,n=t.length,r,l="value"in en?en.value:en.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===l[o-r];r++);return El=l.slice(e,1<r?1-r:void 0)}function bl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function jl(){return!0}function oa(){return!1}function dt(e){function t(n,r,l,o,a){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=a,this.currentTarget=null;for(var f in e)e.hasOwnProperty(f)&&(n=e[f],this[f]=n?n(o):o[f]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?jl:oa,this.isPropagationStopped=oa,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=jl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=jl)},persist:function(){},isPersistent:jl}),t}var Dn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yo=dt(Dn),Cr=I({},Dn,{view:0,detail:0}),Cd=dt(Cr),Xo,qo,Or,Cl=I({},Cr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Jo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Or&&(Or&&e.type==="mousemove"?(Xo=e.screenX-Or.screenX,qo=e.screenY-Or.screenY):qo=Xo=0,Or=e),Xo)},movementY:function(e){return"movementY"in e?e.movementY:qo}}),sa=dt(Cl),Od=I({},Cl,{dataTransfer:0}),zd=dt(Od),$d=I({},Cr,{relatedTarget:0}),Zo=dt($d),Ad=I({},Dn,{animationName:0,elapsedTime:0,pseudoElement:0}),Td=dt(Ad),Ld=I({},Dn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Rd=dt(Ld),Fd=I({},Dn,{data:0}),ia=dt(Fd),Id={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ud={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Md={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Md[e])?!!t[e]:!1}function Jo(){return Vd}var Dd=I({},Cr,{key:function(e){if(e.key){var t=Id[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=bl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ud[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Jo,charCode:function(e){return e.type==="keypress"?bl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?bl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bd=dt(Dd),Wd=I({},Cl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),aa=dt(Wd),Hd=I({},Cr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Jo}),Kd=dt(Hd),Qd=I({},Dn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Gd=dt(Qd),Yd=I({},Cl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Xd=dt(Yd),qd=[9,13,27,32],es=P&&"CompositionEvent"in window,zr=null;P&&"documentMode"in document&&(zr=document.documentMode);var Zd=P&&"TextEvent"in window&&!zr,ua=P&&(!es||zr&&8<zr&&11>=zr),ca=" ",da=!1;function fa(e,t){switch(e){case"keyup":return qd.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pa(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Bn=!1;function Jd(e,t){switch(e){case"compositionend":return pa(t);case"keypress":return t.which!==32?null:(da=!0,ca);case"textInput":return e=t.data,e===ca&&da?null:e;default:return null}}function ef(e,t){if(Bn)return e==="compositionend"||!es&&fa(e,t)?(e=la(),El=Go=en=null,Bn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ua&&t.locale!=="ko"?null:t.data;default:return null}}var tf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ma(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!tf[e.type]:t==="textarea"}function ha(e,t,n,r){Li(r),t=Tl(t,"onChange"),0<t.length&&(n=new Yo("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $r=null,Ar=null;function nf(e){Aa(e,0)}function Ol(e){var t=Gn(e);if(mr(t))return e}function rf(e,t){if(e==="change")return t}var ga=!1;if(P){var ts;if(P){var ns="oninput"in document;if(!ns){var ya=document.createElement("div");ya.setAttribute("oninput","return;"),ns=typeof ya.oninput=="function"}ts=ns}else ts=!1;ga=ts&&(!document.documentMode||9<document.documentMode)}function va(){$r&&($r.detachEvent("onpropertychange",xa),Ar=$r=null)}function xa(e){if(e.propertyName==="value"&&Ol(Ar)){var t=[];ha(t,Ar,e,Lo(e)),Ui(nf,t)}}function lf(e,t,n){e==="focusin"?(va(),$r=t,Ar=n,$r.attachEvent("onpropertychange",xa)):e==="focusout"&&va()}function of(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ol(Ar)}function sf(e,t){if(e==="click")return Ol(t)}function af(e,t){if(e==="input"||e==="change")return Ol(t)}function uf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kt=typeof Object.is=="function"?Object.is:uf;function Tr(e,t){if(kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!_.call(t,l)||!kt(e[l],t[l]))return!1}return!0}function wa(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sa(e,t){var n=wa(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wa(n)}}function Na(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Na(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ka(){for(var e=window,t=Fn();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Fn(e.document)}return t}function rs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function cf(e){var t=ka(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Na(n.ownerDocument.documentElement,n)){if(r!==null&&rs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Sa(n,o);var a=Sa(n,r);l&&a&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var df=P&&"documentMode"in document&&11>=document.documentMode,Wn=null,ls=null,Lr=null,os=!1;function Pa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;os||Wn==null||Wn!==Fn(r)||(r=Wn,"selectionStart"in r&&rs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Lr&&Tr(Lr,r)||(Lr=r,r=Tl(ls,"onSelect"),0<r.length&&(t=new Yo("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wn)))}function zl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hn={animationend:zl("Animation","AnimationEnd"),animationiteration:zl("Animation","AnimationIteration"),animationstart:zl("Animation","AnimationStart"),transitionend:zl("Transition","TransitionEnd")},ss={},_a={};P&&(_a=document.createElement("div").style,"AnimationEvent"in window||(delete Hn.animationend.animation,delete Hn.animationiteration.animation,delete Hn.animationstart.animation),"TransitionEvent"in window||delete Hn.transitionend.transition);function $l(e){if(ss[e])return ss[e];if(!Hn[e])return e;var t=Hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in _a)return ss[e]=t[n];return e}var Ea=$l("animationend"),ba=$l("animationiteration"),ja=$l("animationstart"),Ca=$l("transitionend"),Oa=new Map,za="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tn(e,t){Oa.set(e,t),k(t,[e])}for(var is=0;is<za.length;is++){var as=za[is],ff=as.toLowerCase(),pf=as[0].toUpperCase()+as.slice(1);tn(ff,"on"+pf)}tn(Ea,"onAnimationEnd"),tn(ba,"onAnimationIteration"),tn(ja,"onAnimationStart"),tn("dblclick","onDoubleClick"),tn("focusin","onFocus"),tn("focusout","onBlur"),tn(Ca,"onTransitionEnd"),E("onMouseEnter",["mouseout","mouseover"]),E("onMouseLeave",["mouseout","mouseover"]),E("onPointerEnter",["pointerout","pointerover"]),E("onPointerLeave",["pointerout","pointerover"]),k("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),k("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),k("onBeforeInput",["compositionend","keypress","textInput","paste"]),k("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),k("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),k("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),mf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Rr));function $a(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,dd(r,t,void 0,e),e.currentTarget=null}function Aa(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var f=r[a],p=f.instance,S=f.currentTarget;if(f=f.listener,p!==o&&l.isPropagationStopped())break e;$a(l,f,S),o=p}else for(a=0;a<r.length;a++){if(f=r[a],p=f.instance,S=f.currentTarget,f=f.listener,p!==o&&l.isPropagationStopped())break e;$a(l,f,S),o=p}}}if(gl)throw e=Uo,gl=!1,Uo=null,e}function ke(e,t){var n=t[gs];n===void 0&&(n=t[gs]=new Set);var r=e+"__bubble";n.has(r)||(Ta(t,e,2,!1),n.add(r))}function us(e,t,n){var r=0;t&&(r|=4),Ta(n,e,r,t)}var Al="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[Al]){e[Al]=!0,y.forEach(function(n){n!=="selectionchange"&&(mf.has(n)||us(n,!1,e),us(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Al]||(t[Al]=!0,us("selectionchange",!1,t))}}function Ta(e,t,n,r){switch(ra(t)){case 1:var l=bd;break;case 4:l=jd;break;default:l=Ko}n=l.bind(null,t,n,e),l=void 0,!Io||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function cs(e,t,n,r,l){var o=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var f=r.stateNode.containerInfo;if(f===l||f.nodeType===8&&f.parentNode===l)break;if(a===4)for(a=r.return;a!==null;){var p=a.tag;if((p===3||p===4)&&(p=a.stateNode.containerInfo,p===l||p.nodeType===8&&p.parentNode===l))return;a=a.return}for(;f!==null;){if(a=kn(f),a===null)return;if(p=a.tag,p===5||p===6){r=o=a;continue e}f=f.parentNode}}r=r.return}Ui(function(){var S=o,C=Lo(n),z=[];e:{var j=Oa.get(e);if(j!==void 0){var U=Yo,D=e;switch(e){case"keypress":if(bl(n)===0)break e;case"keydown":case"keyup":U=Bd;break;case"focusin":D="focus",U=Zo;break;case"focusout":D="blur",U=Zo;break;case"beforeblur":case"afterblur":U=Zo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=sa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=zd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=Kd;break;case Ea:case ba:case ja:U=Td;break;case Ca:U=Gd;break;case"scroll":U=Cd;break;case"wheel":U=Xd;break;case"copy":case"cut":case"paste":U=Rd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=aa}var B=(t&4)!==0,Ae=!B&&e==="scroll",x=B?j!==null?j+"Capture":null:j;B=[];for(var h=S,w;h!==null;){w=h;var A=w.stateNode;if(w.tag===5&&A!==null&&(w=A,x!==null&&(A=xr(h,x),A!=null&&B.push(Ir(h,A,w)))),Ae)break;h=h.return}0<B.length&&(j=new U(j,D,null,n,C),z.push({event:j,listeners:B}))}}if((t&7)===0){e:{if(j=e==="mouseover"||e==="pointerover",U=e==="mouseout"||e==="pointerout",j&&n!==To&&(D=n.relatedTarget||n.fromElement)&&(kn(D)||D[Ft]))break e;if((U||j)&&(j=C.window===C?C:(j=C.ownerDocument)?j.defaultView||j.parentWindow:window,U?(D=n.relatedTarget||n.toElement,U=S,D=D?kn(D):null,D!==null&&(Ae=Nn(D),D!==Ae||D.tag!==5&&D.tag!==6)&&(D=null)):(U=null,D=S),U!==D)){if(B=sa,A="onMouseLeave",x="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(B=aa,A="onPointerLeave",x="onPointerEnter",h="pointer"),Ae=U==null?j:Gn(U),w=D==null?j:Gn(D),j=new B(A,h+"leave",U,n,C),j.target=Ae,j.relatedTarget=w,A=null,kn(C)===S&&(B=new B(x,h+"enter",D,n,C),B.target=w,B.relatedTarget=Ae,A=B),Ae=A,U&&D)t:{for(B=U,x=D,h=0,w=B;w;w=Kn(w))h++;for(w=0,A=x;A;A=Kn(A))w++;for(;0<h-w;)B=Kn(B),h--;for(;0<w-h;)x=Kn(x),w--;for(;h--;){if(B===x||x!==null&&B===x.alternate)break t;B=Kn(B),x=Kn(x)}B=null}else B=null;U!==null&&La(z,j,U,B,!1),D!==null&&Ae!==null&&La(z,Ae,D,B,!0)}}e:{if(j=S?Gn(S):window,U=j.nodeName&&j.nodeName.toLowerCase(),U==="select"||U==="input"&&j.type==="file")var W=rf;else if(ma(j))if(ga)W=af;else{W=of;var Q=lf}else(U=j.nodeName)&&U.toLowerCase()==="input"&&(j.type==="checkbox"||j.type==="radio")&&(W=sf);if(W&&(W=W(e,S))){ha(z,W,n,C);break e}Q&&Q(e,j,S),e==="focusout"&&(Q=j._wrapperState)&&Q.controlled&&j.type==="number"&&v(j,"number",j.value)}switch(Q=S?Gn(S):window,e){case"focusin":(ma(Q)||Q.contentEditable==="true")&&(Wn=Q,ls=S,Lr=null);break;case"focusout":Lr=ls=Wn=null;break;case"mousedown":os=!0;break;case"contextmenu":case"mouseup":case"dragend":os=!1,Pa(z,n,C);break;case"selectionchange":if(df)break;case"keydown":case"keyup":Pa(z,n,C)}var G;if(es)e:{switch(e){case"compositionstart":var q="onCompositionStart";break e;case"compositionend":q="onCompositionEnd";break e;case"compositionupdate":q="onCompositionUpdate";break e}q=void 0}else Bn?fa(e,n)&&(q="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(q="onCompositionStart");q&&(ua&&n.locale!=="ko"&&(Bn||q!=="onCompositionStart"?q==="onCompositionEnd"&&Bn&&(G=la()):(en=C,Go="value"in en?en.value:en.textContent,Bn=!0)),Q=Tl(S,q),0<Q.length&&(q=new ia(q,e,null,n,C),z.push({event:q,listeners:Q}),G?q.data=G:(G=pa(n),G!==null&&(q.data=G)))),(G=Zd?Jd(e,n):ef(e,n))&&(S=Tl(S,"onBeforeInput"),0<S.length&&(C=new ia("onBeforeInput","beforeinput",null,n,C),z.push({event:C,listeners:S}),C.data=G))}Aa(z,t)})}function Ir(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Tl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=xr(e,n),o!=null&&r.unshift(Ir(e,o,l)),o=xr(e,t),o!=null&&r.push(Ir(e,o,l))),e=e.return}return r}function Kn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function La(e,t,n,r,l){for(var o=t._reactName,a=[];n!==null&&n!==r;){var f=n,p=f.alternate,S=f.stateNode;if(p!==null&&p===r)break;f.tag===5&&S!==null&&(f=S,l?(p=xr(n,o),p!=null&&a.unshift(Ir(n,p,f))):l||(p=xr(n,o),p!=null&&a.push(Ir(n,p,f)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var hf=/\r\n?/g,gf=/\u0000|\uFFFD/g;function Ra(e){return(typeof e=="string"?e:""+e).replace(hf,`
`).replace(gf,"")}function Ll(e,t,n){if(t=Ra(t),Ra(e)!==t&&n)throw Error(i(425))}function Rl(){}var ds=null,fs=null;function ps(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ms=typeof setTimeout=="function"?setTimeout:void 0,yf=typeof clearTimeout=="function"?clearTimeout:void 0,Fa=typeof Promise=="function"?Promise:void 0,vf=typeof queueMicrotask=="function"?queueMicrotask:typeof Fa<"u"?function(e){return Fa.resolve(null).then(e).catch(xf)}:ms;function xf(e){setTimeout(function(){throw e})}function hs(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),jr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);jr(t)}function nn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ia(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Qn=Math.random().toString(36).slice(2),zt="__reactFiber$"+Qn,Ur="__reactProps$"+Qn,Ft="__reactContainer$"+Qn,gs="__reactEvents$"+Qn,wf="__reactListeners$"+Qn,Sf="__reactHandles$"+Qn;function kn(e){var t=e[zt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ft]||n[zt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ia(e);e!==null;){if(n=e[zt])return n;e=Ia(e)}return t}e=n,n=e.parentNode}return null}function Mr(e){return e=e[zt]||e[Ft],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function Fl(e){return e[Ur]||null}var ys=[],Yn=-1;function rn(e){return{current:e}}function Pe(e){0>Yn||(e.current=ys[Yn],ys[Yn]=null,Yn--)}function Ne(e,t){Yn++,ys[Yn]=e.current,e.current=t}var ln={},Ge=rn(ln),lt=rn(!1),Pn=ln;function Xn(e,t){var n=e.type.contextTypes;if(!n)return ln;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ot(e){return e=e.childContextTypes,e!=null}function Il(){Pe(lt),Pe(Ge)}function Ua(e,t,n){if(Ge.current!==ln)throw Error(i(168));Ne(Ge,t),Ne(lt,n)}function Ma(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(i(108,we(e)||"Unknown",l));return I({},n,r)}function Ul(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,Pn=Ge.current,Ne(Ge,e),Ne(lt,lt.current),!0}function Va(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Ma(e,t,Pn),r.__reactInternalMemoizedMergedChildContext=e,Pe(lt),Pe(Ge),Ne(Ge,e)):Pe(lt),Ne(lt,n)}var It=null,Ml=!1,vs=!1;function Da(e){It===null?It=[e]:It.push(e)}function Nf(e){Ml=!0,Da(e)}function on(){if(!vs&&It!==null){vs=!0;var e=0,t=ve;try{var n=It;for(ve=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}It=null,Ml=!1}catch(l){throw It!==null&&(It=It.slice(e+1)),Wi(Mo,on),l}finally{ve=t,vs=!1}}return null}var qn=[],Zn=0,Vl=null,Dl=0,ht=[],gt=0,_n=null,Ut=1,Mt="";function En(e,t){qn[Zn++]=Dl,qn[Zn++]=Vl,Vl=e,Dl=t}function Ba(e,t,n){ht[gt++]=Ut,ht[gt++]=Mt,ht[gt++]=_n,_n=e;var r=Ut;e=Mt;var l=32-Nt(r)-1;r&=~(1<<l),n+=1;var o=32-Nt(t)+l;if(30<o){var a=l-l%5;o=(r&(1<<a)-1).toString(32),r>>=a,l-=a,Ut=1<<32-Nt(t)+l|n<<l|r,Mt=o+e}else Ut=1<<o|n<<l|r,Mt=e}function xs(e){e.return!==null&&(En(e,1),Ba(e,1,0))}function ws(e){for(;e===Vl;)Vl=qn[--Zn],qn[Zn]=null,Dl=qn[--Zn],qn[Zn]=null;for(;e===_n;)_n=ht[--gt],ht[gt]=null,Mt=ht[--gt],ht[gt]=null,Ut=ht[--gt],ht[gt]=null}var ft=null,pt=null,Ee=!1,Pt=null;function Wa(e,t){var n=wt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ha(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ft=e,pt=nn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ft=e,pt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=_n!==null?{id:Ut,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=wt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ft=e,pt=null,!0):!1;default:return!1}}function Ss(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ns(e){if(Ee){var t=pt;if(t){var n=t;if(!Ha(e,t)){if(Ss(e))throw Error(i(418));t=nn(n.nextSibling);var r=ft;t&&Ha(e,t)?Wa(r,n):(e.flags=e.flags&-4097|2,Ee=!1,ft=e)}}else{if(Ss(e))throw Error(i(418));e.flags=e.flags&-4097|2,Ee=!1,ft=e}}}function Ka(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ft=e}function Bl(e){if(e!==ft)return!1;if(!Ee)return Ka(e),Ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ps(e.type,e.memoizedProps)),t&&(t=pt)){if(Ss(e))throw Qa(),Error(i(418));for(;t;)Wa(e,t),t=nn(t.nextSibling)}if(Ka(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){pt=nn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}pt=null}}else pt=ft?nn(e.stateNode.nextSibling):null;return!0}function Qa(){for(var e=pt;e;)e=nn(e.nextSibling)}function Jn(){pt=ft=null,Ee=!1}function ks(e){Pt===null?Pt=[e]:Pt.push(e)}var kf=me.ReactCurrentBatchConfig;function Vr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(a){var f=l.refs;a===null?delete f[o]:f[o]=a},t._stringRef=o,t)}if(typeof e!="string")throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function Wl(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ga(e){var t=e._init;return t(e._payload)}function Ya(e){function t(x,h){if(e){var w=x.deletions;w===null?(x.deletions=[h],x.flags|=16):w.push(h)}}function n(x,h){if(!e)return null;for(;h!==null;)t(x,h),h=h.sibling;return null}function r(x,h){for(x=new Map;h!==null;)h.key!==null?x.set(h.key,h):x.set(h.index,h),h=h.sibling;return x}function l(x,h){return x=mn(x,h),x.index=0,x.sibling=null,x}function o(x,h,w){return x.index=w,e?(w=x.alternate,w!==null?(w=w.index,w<h?(x.flags|=2,h):w):(x.flags|=2,h)):(x.flags|=1048576,h)}function a(x){return e&&x.alternate===null&&(x.flags|=2),x}function f(x,h,w,A){return h===null||h.tag!==6?(h=mi(w,x.mode,A),h.return=x,h):(h=l(h,w),h.return=x,h)}function p(x,h,w,A){var W=w.type;return W===ye?C(x,h,w.props.children,A,w.key):h!==null&&(h.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Oe&&Ga(W)===h.type)?(A=l(h,w.props),A.ref=Vr(x,h,w),A.return=x,A):(A=ho(w.type,w.key,w.props,null,x.mode,A),A.ref=Vr(x,h,w),A.return=x,A)}function S(x,h,w,A){return h===null||h.tag!==4||h.stateNode.containerInfo!==w.containerInfo||h.stateNode.implementation!==w.implementation?(h=hi(w,x.mode,A),h.return=x,h):(h=l(h,w.children||[]),h.return=x,h)}function C(x,h,w,A,W){return h===null||h.tag!==7?(h=Tn(w,x.mode,A,W),h.return=x,h):(h=l(h,w),h.return=x,h)}function z(x,h,w){if(typeof h=="string"&&h!==""||typeof h=="number")return h=mi(""+h,x.mode,w),h.return=x,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Te:return w=ho(h.type,h.key,h.props,null,x.mode,w),w.ref=Vr(x,null,h),w.return=x,w;case Z:return h=hi(h,x.mode,w),h.return=x,h;case Oe:var A=h._init;return z(x,A(h._payload),w)}if(m(h)||K(h))return h=Tn(h,x.mode,w,null),h.return=x,h;Wl(x,h)}return null}function j(x,h,w,A){var W=h!==null?h.key:null;if(typeof w=="string"&&w!==""||typeof w=="number")return W!==null?null:f(x,h,""+w,A);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Te:return w.key===W?p(x,h,w,A):null;case Z:return w.key===W?S(x,h,w,A):null;case Oe:return W=w._init,j(x,h,W(w._payload),A)}if(m(w)||K(w))return W!==null?null:C(x,h,w,A,null);Wl(x,w)}return null}function U(x,h,w,A,W){if(typeof A=="string"&&A!==""||typeof A=="number")return x=x.get(w)||null,f(h,x,""+A,W);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case Te:return x=x.get(A.key===null?w:A.key)||null,p(h,x,A,W);case Z:return x=x.get(A.key===null?w:A.key)||null,S(h,x,A,W);case Oe:var Q=A._init;return U(x,h,w,Q(A._payload),W)}if(m(A)||K(A))return x=x.get(w)||null,C(h,x,A,W,null);Wl(h,A)}return null}function D(x,h,w,A){for(var W=null,Q=null,G=h,q=h=0,We=null;G!==null&&q<w.length;q++){G.index>q?(We=G,G=null):We=G.sibling;var pe=j(x,G,w[q],A);if(pe===null){G===null&&(G=We);break}e&&G&&pe.alternate===null&&t(x,G),h=o(pe,h,q),Q===null?W=pe:Q.sibling=pe,Q=pe,G=We}if(q===w.length)return n(x,G),Ee&&En(x,q),W;if(G===null){for(;q<w.length;q++)G=z(x,w[q],A),G!==null&&(h=o(G,h,q),Q===null?W=G:Q.sibling=G,Q=G);return Ee&&En(x,q),W}for(G=r(x,G);q<w.length;q++)We=U(G,x,q,w[q],A),We!==null&&(e&&We.alternate!==null&&G.delete(We.key===null?q:We.key),h=o(We,h,q),Q===null?W=We:Q.sibling=We,Q=We);return e&&G.forEach(function(hn){return t(x,hn)}),Ee&&En(x,q),W}function B(x,h,w,A){var W=K(w);if(typeof W!="function")throw Error(i(150));if(w=W.call(w),w==null)throw Error(i(151));for(var Q=W=null,G=h,q=h=0,We=null,pe=w.next();G!==null&&!pe.done;q++,pe=w.next()){G.index>q?(We=G,G=null):We=G.sibling;var hn=j(x,G,pe.value,A);if(hn===null){G===null&&(G=We);break}e&&G&&hn.alternate===null&&t(x,G),h=o(hn,h,q),Q===null?W=hn:Q.sibling=hn,Q=hn,G=We}if(pe.done)return n(x,G),Ee&&En(x,q),W;if(G===null){for(;!pe.done;q++,pe=w.next())pe=z(x,pe.value,A),pe!==null&&(h=o(pe,h,q),Q===null?W=pe:Q.sibling=pe,Q=pe);return Ee&&En(x,q),W}for(G=r(x,G);!pe.done;q++,pe=w.next())pe=U(G,x,q,pe.value,A),pe!==null&&(e&&pe.alternate!==null&&G.delete(pe.key===null?q:pe.key),h=o(pe,h,q),Q===null?W=pe:Q.sibling=pe,Q=pe);return e&&G.forEach(function(np){return t(x,np)}),Ee&&En(x,q),W}function Ae(x,h,w,A){if(typeof w=="object"&&w!==null&&w.type===ye&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case Te:e:{for(var W=w.key,Q=h;Q!==null;){if(Q.key===W){if(W=w.type,W===ye){if(Q.tag===7){n(x,Q.sibling),h=l(Q,w.props.children),h.return=x,x=h;break e}}else if(Q.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Oe&&Ga(W)===Q.type){n(x,Q.sibling),h=l(Q,w.props),h.ref=Vr(x,Q,w),h.return=x,x=h;break e}n(x,Q);break}else t(x,Q);Q=Q.sibling}w.type===ye?(h=Tn(w.props.children,x.mode,A,w.key),h.return=x,x=h):(A=ho(w.type,w.key,w.props,null,x.mode,A),A.ref=Vr(x,h,w),A.return=x,x=A)}return a(x);case Z:e:{for(Q=w.key;h!==null;){if(h.key===Q)if(h.tag===4&&h.stateNode.containerInfo===w.containerInfo&&h.stateNode.implementation===w.implementation){n(x,h.sibling),h=l(h,w.children||[]),h.return=x,x=h;break e}else{n(x,h);break}else t(x,h);h=h.sibling}h=hi(w,x.mode,A),h.return=x,x=h}return a(x);case Oe:return Q=w._init,Ae(x,h,Q(w._payload),A)}if(m(w))return D(x,h,w,A);if(K(w))return B(x,h,w,A);Wl(x,w)}return typeof w=="string"&&w!==""||typeof w=="number"?(w=""+w,h!==null&&h.tag===6?(n(x,h.sibling),h=l(h,w),h.return=x,x=h):(n(x,h),h=mi(w,x.mode,A),h.return=x,x=h),a(x)):n(x,h)}return Ae}var er=Ya(!0),Xa=Ya(!1),Hl=rn(null),Kl=null,tr=null,Ps=null;function _s(){Ps=tr=Kl=null}function Es(e){var t=Hl.current;Pe(Hl),e._currentValue=t}function bs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function nr(e,t){Kl=e,Ps=tr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(st=!0),e.firstContext=null)}function yt(e){var t=e._currentValue;if(Ps!==e)if(e={context:e,memoizedValue:t,next:null},tr===null){if(Kl===null)throw Error(i(308));tr=e,Kl.dependencies={lanes:0,firstContext:e}}else tr=tr.next=e;return t}var bn=null;function js(e){bn===null?bn=[e]:bn.push(e)}function qa(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,js(t)):(n.next=l.next,l.next=n),t.interleaved=n,Vt(e,r)}function Vt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var sn=!1;function Cs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Za(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function an(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(fe&2)!==0){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Vt(e,n)}return l=r.interleaved,l===null?(t.next=t,js(r)):(t.next=l.next,l.next=t),r.interleaved=t,Vt(e,n)}function Ql(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Bo(e,n)}}function Ja(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=a:o=o.next=a,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Gl(e,t,n,r){var l=e.updateQueue;sn=!1;var o=l.firstBaseUpdate,a=l.lastBaseUpdate,f=l.shared.pending;if(f!==null){l.shared.pending=null;var p=f,S=p.next;p.next=null,a===null?o=S:a.next=S,a=p;var C=e.alternate;C!==null&&(C=C.updateQueue,f=C.lastBaseUpdate,f!==a&&(f===null?C.firstBaseUpdate=S:f.next=S,C.lastBaseUpdate=p))}if(o!==null){var z=l.baseState;a=0,C=S=p=null,f=o;do{var j=f.lane,U=f.eventTime;if((r&j)===j){C!==null&&(C=C.next={eventTime:U,lane:0,tag:f.tag,payload:f.payload,callback:f.callback,next:null});e:{var D=e,B=f;switch(j=t,U=n,B.tag){case 1:if(D=B.payload,typeof D=="function"){z=D.call(U,z,j);break e}z=D;break e;case 3:D.flags=D.flags&-65537|128;case 0:if(D=B.payload,j=typeof D=="function"?D.call(U,z,j):D,j==null)break e;z=I({},z,j);break e;case 2:sn=!0}}f.callback!==null&&f.lane!==0&&(e.flags|=64,j=l.effects,j===null?l.effects=[f]:j.push(f))}else U={eventTime:U,lane:j,tag:f.tag,payload:f.payload,callback:f.callback,next:null},C===null?(S=C=U,p=z):C=C.next=U,a|=j;if(f=f.next,f===null){if(f=l.shared.pending,f===null)break;j=f,f=j.next,j.next=null,l.lastBaseUpdate=j,l.shared.pending=null}}while(!0);if(C===null&&(p=z),l.baseState=p,l.firstBaseUpdate=S,l.lastBaseUpdate=C,t=l.shared.interleaved,t!==null){l=t;do a|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);On|=a,e.lanes=a,e.memoizedState=z}}function eu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(i(191,l));l.call(r)}}}var Dr={},$t=rn(Dr),Br=rn(Dr),Wr=rn(Dr);function jn(e){if(e===Dr)throw Error(i(174));return e}function Os(e,t){switch(Ne(Wr,t),Ne(Br,e),Ne($t,Dr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:In(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=In(t,e)}Pe($t),Ne($t,t)}function rr(){Pe($t),Pe(Br),Pe(Wr)}function tu(e){jn(Wr.current);var t=jn($t.current),n=In(t,e.type);t!==n&&(Ne(Br,e),Ne($t,n))}function zs(e){Br.current===e&&(Pe($t),Pe(Br))}var be=rn(0);function Yl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var $s=[];function As(){for(var e=0;e<$s.length;e++)$s[e]._workInProgressVersionPrimary=null;$s.length=0}var Xl=me.ReactCurrentDispatcher,Ts=me.ReactCurrentBatchConfig,Cn=0,je=null,Ie=null,De=null,ql=!1,Hr=!1,Kr=0,Pf=0;function Ye(){throw Error(i(321))}function Ls(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!kt(e[n],t[n]))return!1;return!0}function Rs(e,t,n,r,l,o){if(Cn=o,je=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xl.current=e===null||e.memoizedState===null?jf:Cf,e=n(r,l),Hr){o=0;do{if(Hr=!1,Kr=0,25<=o)throw Error(i(301));o+=1,De=Ie=null,t.updateQueue=null,Xl.current=Of,e=n(r,l)}while(Hr)}if(Xl.current=eo,t=Ie!==null&&Ie.next!==null,Cn=0,De=Ie=je=null,ql=!1,t)throw Error(i(300));return e}function Fs(){var e=Kr!==0;return Kr=0,e}function At(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return De===null?je.memoizedState=De=e:De=De.next=e,De}function vt(){if(Ie===null){var e=je.alternate;e=e!==null?e.memoizedState:null}else e=Ie.next;var t=De===null?je.memoizedState:De.next;if(t!==null)De=t,Ie=e;else{if(e===null)throw Error(i(310));Ie=e,e={memoizedState:Ie.memoizedState,baseState:Ie.baseState,baseQueue:Ie.baseQueue,queue:Ie.queue,next:null},De===null?je.memoizedState=De=e:De=De.next=e}return De}function Qr(e,t){return typeof t=="function"?t(e):t}function Is(e){var t=vt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=Ie,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var a=l.next;l.next=o.next,o.next=a}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var f=a=null,p=null,S=o;do{var C=S.lane;if((Cn&C)===C)p!==null&&(p=p.next={lane:0,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null}),r=S.hasEagerState?S.eagerState:e(r,S.action);else{var z={lane:C,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null};p===null?(f=p=z,a=r):p=p.next=z,je.lanes|=C,On|=C}S=S.next}while(S!==null&&S!==o);p===null?a=r:p.next=f,kt(r,t.memoizedState)||(st=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=p,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,je.lanes|=o,On|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Us(e){var t=vt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var a=l=l.next;do o=e(o,a.action),a=a.next;while(a!==l);kt(o,t.memoizedState)||(st=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function nu(){}function ru(e,t){var n=je,r=vt(),l=t(),o=!kt(r.memoizedState,l);if(o&&(r.memoizedState=l,st=!0),r=r.queue,Ms(su.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||De!==null&&De.memoizedState.tag&1){if(n.flags|=2048,Gr(9,ou.bind(null,n,r,l,t),void 0,null),Be===null)throw Error(i(349));(Cn&30)!==0||lu(n,t,l)}return l}function lu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=je.updateQueue,t===null?(t={lastEffect:null,stores:null},je.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ou(e,t,n,r){t.value=n,t.getSnapshot=r,iu(t)&&au(e)}function su(e,t,n){return n(function(){iu(t)&&au(e)})}function iu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!kt(e,n)}catch{return!0}}function au(e){var t=Vt(e,1);t!==null&&jt(t,e,1,-1)}function uu(e){var t=At();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qr,lastRenderedState:e},t.queue=e,e=e.dispatch=bf.bind(null,je,e),[t.memoizedState,e]}function Gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=je.updateQueue,t===null?(t={lastEffect:null,stores:null},je.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function cu(){return vt().memoizedState}function Zl(e,t,n,r){var l=At();je.flags|=e,l.memoizedState=Gr(1|t,n,void 0,r===void 0?null:r)}function Jl(e,t,n,r){var l=vt();r=r===void 0?null:r;var o=void 0;if(Ie!==null){var a=Ie.memoizedState;if(o=a.destroy,r!==null&&Ls(r,a.deps)){l.memoizedState=Gr(t,n,o,r);return}}je.flags|=e,l.memoizedState=Gr(1|t,n,o,r)}function du(e,t){return Zl(8390656,8,e,t)}function Ms(e,t){return Jl(2048,8,e,t)}function fu(e,t){return Jl(4,2,e,t)}function pu(e,t){return Jl(4,4,e,t)}function mu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hu(e,t,n){return n=n!=null?n.concat([e]):null,Jl(4,4,mu.bind(null,t,e),n)}function Vs(){}function gu(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ls(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function yu(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ls(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vu(e,t,n){return(Cn&21)===0?(e.baseState&&(e.baseState=!1,st=!0),e.memoizedState=n):(kt(n,t)||(n=Gi(),je.lanes|=n,On|=n,e.baseState=!0),t)}function _f(e,t){var n=ve;ve=n!==0&&4>n?n:4,e(!0);var r=Ts.transition;Ts.transition={};try{e(!1),t()}finally{ve=n,Ts.transition=r}}function xu(){return vt().memoizedState}function Ef(e,t,n){var r=fn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},wu(e))Su(t,n);else if(n=qa(e,t,n,r),n!==null){var l=nt();jt(n,e,r,l),Nu(n,t,r)}}function bf(e,t,n){var r=fn(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(wu(e))Su(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var a=t.lastRenderedState,f=o(a,n);if(l.hasEagerState=!0,l.eagerState=f,kt(f,a)){var p=t.interleaved;p===null?(l.next=l,js(t)):(l.next=p.next,p.next=l),t.interleaved=l;return}}catch{}finally{}n=qa(e,t,l,r),n!==null&&(l=nt(),jt(n,e,r,l),Nu(n,t,r))}}function wu(e){var t=e.alternate;return e===je||t!==null&&t===je}function Su(e,t){Hr=ql=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Nu(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Bo(e,n)}}var eo={readContext:yt,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useInsertionEffect:Ye,useLayoutEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useMutableSource:Ye,useSyncExternalStore:Ye,useId:Ye,unstable_isNewReconciler:!1},jf={readContext:yt,useCallback:function(e,t){return At().memoizedState=[e,t===void 0?null:t],e},useContext:yt,useEffect:du,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Zl(4194308,4,mu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Zl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Zl(4,2,e,t)},useMemo:function(e,t){var n=At();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=At();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ef.bind(null,je,e),[r.memoizedState,e]},useRef:function(e){var t=At();return e={current:e},t.memoizedState=e},useState:uu,useDebugValue:Vs,useDeferredValue:function(e){return At().memoizedState=e},useTransition:function(){var e=uu(!1),t=e[0];return e=_f.bind(null,e[1]),At().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=je,l=At();if(Ee){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),Be===null)throw Error(i(349));(Cn&30)!==0||lu(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,du(su.bind(null,r,o,e),[e]),r.flags|=2048,Gr(9,ou.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=At(),t=Be.identifierPrefix;if(Ee){var n=Mt,r=Ut;n=(r&~(1<<32-Nt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Kr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Pf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Cf={readContext:yt,useCallback:gu,useContext:yt,useEffect:Ms,useImperativeHandle:hu,useInsertionEffect:fu,useLayoutEffect:pu,useMemo:yu,useReducer:Is,useRef:cu,useState:function(){return Is(Qr)},useDebugValue:Vs,useDeferredValue:function(e){var t=vt();return vu(t,Ie.memoizedState,e)},useTransition:function(){var e=Is(Qr)[0],t=vt().memoizedState;return[e,t]},useMutableSource:nu,useSyncExternalStore:ru,useId:xu,unstable_isNewReconciler:!1},Of={readContext:yt,useCallback:gu,useContext:yt,useEffect:Ms,useImperativeHandle:hu,useInsertionEffect:fu,useLayoutEffect:pu,useMemo:yu,useReducer:Us,useRef:cu,useState:function(){return Us(Qr)},useDebugValue:Vs,useDeferredValue:function(e){var t=vt();return Ie===null?t.memoizedState=e:vu(t,Ie.memoizedState,e)},useTransition:function(){var e=Us(Qr)[0],t=vt().memoizedState;return[e,t]},useMutableSource:nu,useSyncExternalStore:ru,useId:xu,unstable_isNewReconciler:!1};function _t(e,t){if(e&&e.defaultProps){t=I({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ds(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:I({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var to={isMounted:function(e){return(e=e._reactInternals)?Nn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=nt(),l=fn(e),o=Dt(r,l);o.payload=t,n!=null&&(o.callback=n),t=an(e,o,l),t!==null&&(jt(t,e,l,r),Ql(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=nt(),l=fn(e),o=Dt(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=an(e,o,l),t!==null&&(jt(t,e,l,r),Ql(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=nt(),r=fn(e),l=Dt(n,r);l.tag=2,t!=null&&(l.callback=t),t=an(e,l,r),t!==null&&(jt(t,e,r,n),Ql(t,e,r))}};function ku(e,t,n,r,l,o,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,a):t.prototype&&t.prototype.isPureReactComponent?!Tr(n,r)||!Tr(l,o):!0}function Pu(e,t,n){var r=!1,l=ln,o=t.contextType;return typeof o=="object"&&o!==null?o=yt(o):(l=ot(t)?Pn:Ge.current,r=t.contextTypes,o=(r=r!=null)?Xn(e,l):ln),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=to,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function _u(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&to.enqueueReplaceState(t,t.state,null)}function Bs(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Cs(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=yt(o):(o=ot(t)?Pn:Ge.current,l.context=Xn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Ds(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&to.enqueueReplaceState(l,l.state,null),Gl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function lr(e,t){try{var n="",r=t;do n+=se(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function Ws(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hs(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var zf=typeof WeakMap=="function"?WeakMap:Map;function Eu(e,t,n){n=Dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ao||(ao=!0,si=r),Hs(e,t)},n}function bu(e,t,n){n=Dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Hs(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Hs(e,t),typeof r!="function"&&(cn===null?cn=new Set([this]):cn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function ju(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new zf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Hf.bind(null,e,t,n),t.then(e,e))}function Cu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ou(e,t,n,r,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Dt(-1,1),t.tag=2,an(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var $f=me.ReactCurrentOwner,st=!1;function tt(e,t,n,r){t.child=e===null?Xa(t,null,n,r):er(t,e.child,n,r)}function zu(e,t,n,r,l){n=n.render;var o=t.ref;return nr(t,l),r=Rs(e,t,n,r,o,l),n=Fs(),e!==null&&!st?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Bt(e,t,l)):(Ee&&n&&xs(t),t.flags|=1,tt(e,t,r,l),t.child)}function $u(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!pi(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Au(e,t,o,r,l)):(e=ho(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var a=o.memoizedProps;if(n=n.compare,n=n!==null?n:Tr,n(a,r)&&e.ref===t.ref)return Bt(e,t,l)}return t.flags|=1,e=mn(o,r),e.ref=t.ref,e.return=t,t.child=e}function Au(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Tr(o,r)&&e.ref===t.ref)if(st=!1,t.pendingProps=r=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(st=!0);else return t.lanes=e.lanes,Bt(e,t,l)}return Ks(e,t,n,r,l)}function Tu(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ne(sr,mt),mt|=n;else{if((n&1073741824)===0)return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ne(sr,mt),mt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,Ne(sr,mt),mt|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ne(sr,mt),mt|=r;return tt(e,t,l,n),t.child}function Lu(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ks(e,t,n,r,l){var o=ot(n)?Pn:Ge.current;return o=Xn(t,o),nr(t,l),n=Rs(e,t,n,r,o,l),r=Fs(),e!==null&&!st?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Bt(e,t,l)):(Ee&&r&&xs(t),t.flags|=1,tt(e,t,n,l),t.child)}function Ru(e,t,n,r,l){if(ot(n)){var o=!0;Ul(t)}else o=!1;if(nr(t,l),t.stateNode===null)ro(e,t),Pu(t,n,r),Bs(t,n,r,l),r=!0;else if(e===null){var a=t.stateNode,f=t.memoizedProps;a.props=f;var p=a.context,S=n.contextType;typeof S=="object"&&S!==null?S=yt(S):(S=ot(n)?Pn:Ge.current,S=Xn(t,S));var C=n.getDerivedStateFromProps,z=typeof C=="function"||typeof a.getSnapshotBeforeUpdate=="function";z||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(f!==r||p!==S)&&_u(t,a,r,S),sn=!1;var j=t.memoizedState;a.state=j,Gl(t,r,a,l),p=t.memoizedState,f!==r||j!==p||lt.current||sn?(typeof C=="function"&&(Ds(t,n,C,r),p=t.memoizedState),(f=sn||ku(t,n,f,r,j,p,S))?(z||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=S,r=f):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Za(e,t),f=t.memoizedProps,S=t.type===t.elementType?f:_t(t.type,f),a.props=S,z=t.pendingProps,j=a.context,p=n.contextType,typeof p=="object"&&p!==null?p=yt(p):(p=ot(n)?Pn:Ge.current,p=Xn(t,p));var U=n.getDerivedStateFromProps;(C=typeof U=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(f!==z||j!==p)&&_u(t,a,r,p),sn=!1,j=t.memoizedState,a.state=j,Gl(t,r,a,l);var D=t.memoizedState;f!==z||j!==D||lt.current||sn?(typeof U=="function"&&(Ds(t,n,U,r),D=t.memoizedState),(S=sn||ku(t,n,S,r,j,D,p)||!1)?(C||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,D,p),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,D,p)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=D),a.props=r,a.state=D,a.context=p,r=S):(typeof a.componentDidUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),r=!1)}return Qs(e,t,n,r,o,l)}function Qs(e,t,n,r,l,o){Lu(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return l&&Va(t,n,!1),Bt(e,t,o);r=t.stateNode,$f.current=t;var f=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=er(t,e.child,null,o),t.child=er(t,null,f,o)):tt(e,t,f,o),t.memoizedState=r.state,l&&Va(t,n,!0),t.child}function Fu(e){var t=e.stateNode;t.pendingContext?Ua(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ua(e,t.context,!1),Os(e,t.containerInfo)}function Iu(e,t,n,r,l){return Jn(),ks(l),t.flags|=256,tt(e,t,n,r),t.child}var Gs={dehydrated:null,treeContext:null,retryLane:0};function Ys(e){return{baseLanes:e,cachePool:null,transitions:null}}function Uu(e,t,n){var r=t.pendingProps,l=be.current,o=!1,a=(t.flags&128)!==0,f;if((f=a)||(f=e!==null&&e.memoizedState===null?!1:(l&2)!==0),f?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),Ne(be,l&1),e===null)return Ns(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(a=r.children,e=r.fallback,o?(r=t.mode,o=t.child,a={mode:"hidden",children:a},(r&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=a):o=go(a,r,0,null),e=Tn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Ys(n),t.memoizedState=Gs,e):Xs(t,a));if(l=e.memoizedState,l!==null&&(f=l.dehydrated,f!==null))return Af(e,t,a,r,f,l,n);if(o){o=r.fallback,a=t.mode,l=e.child,f=l.sibling;var p={mode:"hidden",children:r.children};return(a&1)===0&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=p,t.deletions=null):(r=mn(l,p),r.subtreeFlags=l.subtreeFlags&14680064),f!==null?o=mn(f,o):(o=Tn(o,a,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,a=e.child.memoizedState,a=a===null?Ys(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},o.memoizedState=a,o.childLanes=e.childLanes&~n,t.memoizedState=Gs,r}return o=e.child,e=o.sibling,r=mn(o,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xs(e,t){return t=go({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function no(e,t,n,r){return r!==null&&ks(r),er(t,e.child,null,n),e=Xs(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Af(e,t,n,r,l,o,a){if(n)return t.flags&256?(t.flags&=-257,r=Ws(Error(i(422))),no(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=go({mode:"visible",children:r.children},l,0,null),o=Tn(o,l,a,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,(t.mode&1)!==0&&er(t,e.child,null,a),t.child.memoizedState=Ys(a),t.memoizedState=Gs,o);if((t.mode&1)===0)return no(e,t,a,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var f=r.dgst;return r=f,o=Error(i(419)),r=Ws(o,r,void 0),no(e,t,a,r)}if(f=(a&e.childLanes)!==0,st||f){if(r=Be,r!==null){switch(a&-a){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(r.suspendedLanes|a))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,Vt(e,l),jt(r,e,l,-1))}return fi(),r=Ws(Error(i(421))),no(e,t,a,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Kf.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,pt=nn(l.nextSibling),ft=t,Ee=!0,Pt=null,e!==null&&(ht[gt++]=Ut,ht[gt++]=Mt,ht[gt++]=_n,Ut=e.id,Mt=e.overflow,_n=t),t=Xs(t,r.children),t.flags|=4096,t)}function Mu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),bs(e.return,t,n)}function qs(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function Vu(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(tt(e,t,r.children,n),r=be.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Mu(e,n,t);else if(e.tag===19)Mu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ne(be,r),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Yl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),qs(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Yl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}qs(t,!0,n,null,o);break;case"together":qs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ro(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Bt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),On|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=mn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=mn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Tf(e,t,n){switch(t.tag){case 3:Fu(t),Jn();break;case 5:tu(t);break;case 1:ot(t.type)&&Ul(t);break;case 4:Os(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;Ne(Hl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ne(be,be.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Uu(e,t,n):(Ne(be,be.current&1),e=Bt(e,t,n),e!==null?e.sibling:null);Ne(be,be.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return Vu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),Ne(be,be.current),r)break;return null;case 22:case 23:return t.lanes=0,Tu(e,t,n)}return Bt(e,t,n)}var Du,Zs,Bu,Wu;Du=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Zs=function(){},Bu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,jn($t.current);var o=null;switch(n){case"input":l=hr(e,l),r=hr(e,r),o=[];break;case"select":l=I({},l,{value:void 0}),r=I({},r,{value:void 0}),o=[];break;case"textarea":l=M(e,l),r=M(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Rl)}$o(n,r);var a;n=null;for(S in l)if(!r.hasOwnProperty(S)&&l.hasOwnProperty(S)&&l[S]!=null)if(S==="style"){var f=l[S];for(a in f)f.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else S!=="dangerouslySetInnerHTML"&&S!=="children"&&S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&S!=="autoFocus"&&(N.hasOwnProperty(S)?o||(o=[]):(o=o||[]).push(S,null));for(S in r){var p=r[S];if(f=l!=null?l[S]:void 0,r.hasOwnProperty(S)&&p!==f&&(p!=null||f!=null))if(S==="style")if(f){for(a in f)!f.hasOwnProperty(a)||p&&p.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in p)p.hasOwnProperty(a)&&f[a]!==p[a]&&(n||(n={}),n[a]=p[a])}else n||(o||(o=[]),o.push(S,n)),n=p;else S==="dangerouslySetInnerHTML"?(p=p?p.__html:void 0,f=f?f.__html:void 0,p!=null&&f!==p&&(o=o||[]).push(S,p)):S==="children"?typeof p!="string"&&typeof p!="number"||(o=o||[]).push(S,""+p):S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&(N.hasOwnProperty(S)?(p!=null&&S==="onScroll"&&ke("scroll",e),o||f===p||(o=[])):(o=o||[]).push(S,p))}n&&(o=o||[]).push("style",n);var S=o;(t.updateQueue=S)&&(t.flags|=4)}},Wu=function(e,t,n,r){n!==r&&(t.flags|=4)};function Yr(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Lf(e,t,n){var r=t.pendingProps;switch(ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xe(t),null;case 1:return ot(t.type)&&Il(),Xe(t),null;case 3:return r=t.stateNode,rr(),Pe(lt),Pe(Ge),As(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Bl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Pt!==null&&(ui(Pt),Pt=null))),Zs(e,t),Xe(t),null;case 5:zs(t);var l=jn(Wr.current);if(n=t.type,e!==null&&t.stateNode!=null)Bu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Xe(t),null}if(e=jn($t.current),Bl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[zt]=t,r[Ur]=o,e=(t.mode&1)!==0,n){case"dialog":ke("cancel",r),ke("close",r);break;case"iframe":case"object":case"embed":ke("load",r);break;case"video":case"audio":for(l=0;l<Rr.length;l++)ke(Rr[l],r);break;case"source":ke("error",r);break;case"img":case"image":case"link":ke("error",r),ke("load",r);break;case"details":ke("toggle",r);break;case"input":fl(r,o),ke("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},ke("invalid",r);break;case"textarea":F(r,o),ke("invalid",r)}$o(n,o),l=null;for(var a in o)if(o.hasOwnProperty(a)){var f=o[a];a==="children"?typeof f=="string"?r.textContent!==f&&(o.suppressHydrationWarning!==!0&&Ll(r.textContent,f,e),l=["children",f]):typeof f=="number"&&r.textContent!==""+f&&(o.suppressHydrationWarning!==!0&&Ll(r.textContent,f,e),l=["children",""+f]):N.hasOwnProperty(a)&&f!=null&&a==="onScroll"&&ke("scroll",r)}switch(n){case"input":wn(r),c(r,o,!0);break;case"textarea":wn(r),ct(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Rl)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=St(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[zt]=t,e[Ur]=r,Du(e,t,!1,!1),t.stateNode=e;e:{switch(a=Ao(n,r),n){case"dialog":ke("cancel",e),ke("close",e),l=r;break;case"iframe":case"object":case"embed":ke("load",e),l=r;break;case"video":case"audio":for(l=0;l<Rr.length;l++)ke(Rr[l],e);l=r;break;case"source":ke("error",e),l=r;break;case"img":case"image":case"link":ke("error",e),ke("load",e),l=r;break;case"details":ke("toggle",e),l=r;break;case"input":fl(e,r),l=hr(e,r),ke("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=I({},r,{value:void 0}),ke("invalid",e);break;case"textarea":F(e,r),l=M(e,r),ke("invalid",e);break;default:l=r}$o(n,l),f=l;for(o in f)if(f.hasOwnProperty(o)){var p=f[o];o==="style"?Ai(e,p):o==="dangerouslySetInnerHTML"?(p=p?p.__html:void 0,p!=null&&yr(e,p)):o==="children"?typeof p=="string"?(n!=="textarea"||p!=="")&&Gt(e,p):typeof p=="number"&&Gt(e,""+p):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(N.hasOwnProperty(o)?p!=null&&o==="onScroll"&&ke("scroll",e):p!=null&&Ce(e,o,p,a))}switch(n){case"input":wn(e),c(e,r,!1);break;case"textarea":wn(e),ct(e);break;case"option":r.value!=null&&e.setAttribute("value",""+he(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?$(e,!!r.multiple,o,!1):r.defaultValue!=null&&$(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Rl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Xe(t),null;case 6:if(e&&t.stateNode!=null)Wu(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(i(166));if(n=jn(Wr.current),jn($t.current),Bl(t)){if(r=t.stateNode,n=t.memoizedProps,r[zt]=t,(o=r.nodeValue!==n)&&(e=ft,e!==null))switch(e.tag){case 3:Ll(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ll(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[zt]=t,t.stateNode=r}return Xe(t),null;case 13:if(Pe(be),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ee&&pt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Qa(),Jn(),t.flags|=98560,o=!1;else if(o=Bl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(i(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(i(317));o[zt]=t}else Jn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Xe(t),o=!1}else Pt!==null&&(ui(Pt),Pt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(be.current&1)!==0?Ue===0&&(Ue=3):fi())),t.updateQueue!==null&&(t.flags|=4),Xe(t),null);case 4:return rr(),Zs(e,t),e===null&&Fr(t.stateNode.containerInfo),Xe(t),null;case 10:return Es(t.type._context),Xe(t),null;case 17:return ot(t.type)&&Il(),Xe(t),null;case 19:if(Pe(be),o=t.memoizedState,o===null)return Xe(t),null;if(r=(t.flags&128)!==0,a=o.rendering,a===null)if(r)Yr(o,!1);else{if(Ue!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(a=Yl(e),a!==null){for(t.flags|=128,Yr(o,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,a=o.alternate,a===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=a.childLanes,o.lanes=a.lanes,o.child=a.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=a.memoizedProps,o.memoizedState=a.memoizedState,o.updateQueue=a.updateQueue,o.type=a.type,e=a.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ne(be,be.current&1|2),t.child}e=e.sibling}o.tail!==null&&$e()>ir&&(t.flags|=128,r=!0,Yr(o,!1),t.lanes=4194304)}else{if(!r)if(e=Yl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Yr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!a.alternate&&!Ee)return Xe(t),null}else 2*$e()-o.renderingStartTime>ir&&n!==1073741824&&(t.flags|=128,r=!0,Yr(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(n=o.last,n!==null?n.sibling=a:t.child=a,o.last=a)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=$e(),t.sibling=null,n=be.current,Ne(be,r?n&1|2:n&1),t):(Xe(t),null);case 22:case 23:return di(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(mt&1073741824)!==0&&(Xe(t),t.subtreeFlags&6&&(t.flags|=8192)):Xe(t),null;case 24:return null;case 25:return null}throw Error(i(156,t.tag))}function Rf(e,t){switch(ws(t),t.tag){case 1:return ot(t.type)&&Il(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return rr(),Pe(lt),Pe(Ge),As(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return zs(t),null;case 13:if(Pe(be),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));Jn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Pe(be),null;case 4:return rr(),null;case 10:return Es(t.type._context),null;case 22:case 23:return di(),null;case 24:return null;default:return null}}var lo=!1,qe=!1,Ff=typeof WeakSet=="function"?WeakSet:Set,V=null;function or(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ze(e,t,r)}else n.current=null}function Js(e,t,n){try{n()}catch(r){ze(e,t,r)}}var Hu=!1;function If(e,t){if(ds=Pl,e=ka(),rs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var a=0,f=-1,p=-1,S=0,C=0,z=e,j=null;t:for(;;){for(var U;z!==n||l!==0&&z.nodeType!==3||(f=a+l),z!==o||r!==0&&z.nodeType!==3||(p=a+r),z.nodeType===3&&(a+=z.nodeValue.length),(U=z.firstChild)!==null;)j=z,z=U;for(;;){if(z===e)break t;if(j===n&&++S===l&&(f=a),j===o&&++C===r&&(p=a),(U=z.nextSibling)!==null)break;z=j,j=z.parentNode}z=U}n=f===-1||p===-1?null:{start:f,end:p}}else n=null}n=n||{start:0,end:0}}else n=null;for(fs={focusedElem:e,selectionRange:n},Pl=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var D=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(D!==null){var B=D.memoizedProps,Ae=D.memoizedState,x=t.stateNode,h=x.getSnapshotBeforeUpdate(t.elementType===t.type?B:_t(t.type,B),Ae);x.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var w=t.stateNode.containerInfo;w.nodeType===1?w.textContent="":w.nodeType===9&&w.documentElement&&w.removeChild(w.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(A){ze(t,t.return,A)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return D=Hu,Hu=!1,D}function Xr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Js(t,n,o)}l=l.next}while(l!==r)}}function oo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ei(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ku(e){var t=e.alternate;t!==null&&(e.alternate=null,Ku(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[zt],delete t[Ur],delete t[gs],delete t[wf],delete t[Sf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qu(e){return e.tag===5||e.tag===3||e.tag===4}function Gu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ti(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Rl));else if(r!==4&&(e=e.child,e!==null))for(ti(e,t,n),e=e.sibling;e!==null;)ti(e,t,n),e=e.sibling}function ni(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ni(e,t,n),e=e.sibling;e!==null;)ni(e,t,n),e=e.sibling}var Ke=null,Et=!1;function un(e,t,n){for(n=n.child;n!==null;)Yu(e,t,n),n=n.sibling}function Yu(e,t,n){if(Ot&&typeof Ot.onCommitFiberUnmount=="function")try{Ot.onCommitFiberUnmount(vl,n)}catch{}switch(n.tag){case 5:qe||or(n,t);case 6:var r=Ke,l=Et;Ke=null,un(e,t,n),Ke=r,Et=l,Ke!==null&&(Et?(e=Ke,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ke.removeChild(n.stateNode));break;case 18:Ke!==null&&(Et?(e=Ke,n=n.stateNode,e.nodeType===8?hs(e.parentNode,n):e.nodeType===1&&hs(e,n),jr(e)):hs(Ke,n.stateNode));break;case 4:r=Ke,l=Et,Ke=n.stateNode.containerInfo,Et=!0,un(e,t,n),Ke=r,Et=l;break;case 0:case 11:case 14:case 15:if(!qe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,a=o.destroy;o=o.tag,a!==void 0&&((o&2)!==0||(o&4)!==0)&&Js(n,t,a),l=l.next}while(l!==r)}un(e,t,n);break;case 1:if(!qe&&(or(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(f){ze(n,t,f)}un(e,t,n);break;case 21:un(e,t,n);break;case 22:n.mode&1?(qe=(r=qe)||n.memoizedState!==null,un(e,t,n),qe=r):un(e,t,n);break;default:un(e,t,n)}}function Xu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ff),t.forEach(function(r){var l=Qf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,a=t,f=a;e:for(;f!==null;){switch(f.tag){case 5:Ke=f.stateNode,Et=!1;break e;case 3:Ke=f.stateNode.containerInfo,Et=!0;break e;case 4:Ke=f.stateNode.containerInfo,Et=!0;break e}f=f.return}if(Ke===null)throw Error(i(160));Yu(o,a,l),Ke=null,Et=!1;var p=l.alternate;p!==null&&(p.return=null),l.return=null}catch(S){ze(l,t,S)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)qu(t,e),t=t.sibling}function qu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(bt(t,e),Tt(e),r&4){try{Xr(3,e,e.return),oo(3,e)}catch(B){ze(e,e.return,B)}try{Xr(5,e,e.return)}catch(B){ze(e,e.return,B)}}break;case 1:bt(t,e),Tt(e),r&512&&n!==null&&or(n,n.return);break;case 5:if(bt(t,e),Tt(e),r&512&&n!==null&&or(n,n.return),e.flags&32){var l=e.stateNode;try{Gt(l,"")}catch(B){ze(e,e.return,B)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,a=n!==null?n.memoizedProps:o,f=e.type,p=e.updateQueue;if(e.updateQueue=null,p!==null)try{f==="input"&&o.type==="radio"&&o.name!=null&&pl(l,o),Ao(f,a);var S=Ao(f,o);for(a=0;a<p.length;a+=2){var C=p[a],z=p[a+1];C==="style"?Ai(l,z):C==="dangerouslySetInnerHTML"?yr(l,z):C==="children"?Gt(l,z):Ce(l,C,z,S)}switch(f){case"input":gr(l,o);break;case"textarea":de(l,o);break;case"select":var j=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var U=o.value;U!=null?$(l,!!o.multiple,U,!1):j!==!!o.multiple&&(o.defaultValue!=null?$(l,!!o.multiple,o.defaultValue,!0):$(l,!!o.multiple,o.multiple?[]:"",!1))}l[Ur]=o}catch(B){ze(e,e.return,B)}}break;case 6:if(bt(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(i(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(B){ze(e,e.return,B)}}break;case 3:if(bt(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{jr(t.containerInfo)}catch(B){ze(e,e.return,B)}break;case 4:bt(t,e),Tt(e);break;case 13:bt(t,e),Tt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(oi=$e())),r&4&&Xu(e);break;case 22:if(C=n!==null&&n.memoizedState!==null,e.mode&1?(qe=(S=qe)||C,bt(t,e),qe=S):bt(t,e),Tt(e),r&8192){if(S=e.memoizedState!==null,(e.stateNode.isHidden=S)&&!C&&(e.mode&1)!==0)for(V=e,C=e.child;C!==null;){for(z=V=C;V!==null;){switch(j=V,U=j.child,j.tag){case 0:case 11:case 14:case 15:Xr(4,j,j.return);break;case 1:or(j,j.return);var D=j.stateNode;if(typeof D.componentWillUnmount=="function"){r=j,n=j.return;try{t=r,D.props=t.memoizedProps,D.state=t.memoizedState,D.componentWillUnmount()}catch(B){ze(r,n,B)}}break;case 5:or(j,j.return);break;case 22:if(j.memoizedState!==null){ec(z);continue}}U!==null?(U.return=j,V=U):ec(z)}C=C.sibling}e:for(C=null,z=e;;){if(z.tag===5){if(C===null){C=z;try{l=z.stateNode,S?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(f=z.stateNode,p=z.memoizedProps.style,a=p!=null&&p.hasOwnProperty("display")?p.display:null,f.style.display=vr("display",a))}catch(B){ze(e,e.return,B)}}}else if(z.tag===6){if(C===null)try{z.stateNode.nodeValue=S?"":z.memoizedProps}catch(B){ze(e,e.return,B)}}else if((z.tag!==22&&z.tag!==23||z.memoizedState===null||z===e)&&z.child!==null){z.child.return=z,z=z.child;continue}if(z===e)break e;for(;z.sibling===null;){if(z.return===null||z.return===e)break e;C===z&&(C=null),z=z.return}C===z&&(C=null),z.sibling.return=z.return,z=z.sibling}}break;case 19:bt(t,e),Tt(e),r&4&&Xu(e);break;case 21:break;default:bt(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Qu(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Gt(l,""),r.flags&=-33);var o=Gu(e);ni(e,o,l);break;case 3:case 4:var a=r.stateNode.containerInfo,f=Gu(e);ti(e,f,a);break;default:throw Error(i(161))}}catch(p){ze(e,e.return,p)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Uf(e,t,n){V=e,Zu(e)}function Zu(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var l=V,o=l.child;if(l.tag===22&&r){var a=l.memoizedState!==null||lo;if(!a){var f=l.alternate,p=f!==null&&f.memoizedState!==null||qe;f=lo;var S=qe;if(lo=a,(qe=p)&&!S)for(V=l;V!==null;)a=V,p=a.child,a.tag===22&&a.memoizedState!==null?tc(l):p!==null?(p.return=a,V=p):tc(l);for(;o!==null;)V=o,Zu(o),o=o.sibling;V=l,lo=f,qe=S}Ju(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,V=o):Ju(e)}}function Ju(e){for(;V!==null;){var t=V;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:qe||oo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!qe)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:_t(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&eu(t,o,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}eu(t,a,n)}break;case 5:var f=t.stateNode;if(n===null&&t.flags&4){n=f;var p=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":p.autoFocus&&n.focus();break;case"img":p.src&&(n.src=p.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var S=t.alternate;if(S!==null){var C=S.memoizedState;if(C!==null){var z=C.dehydrated;z!==null&&jr(z)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}qe||t.flags&512&&ei(t)}catch(j){ze(t,t.return,j)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function ec(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function tc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{oo(4,t)}catch(p){ze(t,n,p)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(p){ze(t,l,p)}}var o=t.return;try{ei(t)}catch(p){ze(t,o,p)}break;case 5:var a=t.return;try{ei(t)}catch(p){ze(t,a,p)}}}catch(p){ze(t,t.return,p)}if(t===e){V=null;break}var f=t.sibling;if(f!==null){f.return=t.return,V=f;break}V=t.return}}var Mf=Math.ceil,so=me.ReactCurrentDispatcher,ri=me.ReactCurrentOwner,xt=me.ReactCurrentBatchConfig,fe=0,Be=null,Re=null,Qe=0,mt=0,sr=rn(0),Ue=0,qr=null,On=0,io=0,li=0,Zr=null,it=null,oi=0,ir=1/0,Wt=null,ao=!1,si=null,cn=null,uo=!1,dn=null,co=0,Jr=0,ii=null,fo=-1,po=0;function nt(){return(fe&6)!==0?$e():fo!==-1?fo:fo=$e()}function fn(e){return(e.mode&1)===0?1:(fe&2)!==0&&Qe!==0?Qe&-Qe:kf.transition!==null?(po===0&&(po=Gi()),po):(e=ve,e!==0||(e=window.event,e=e===void 0?16:ra(e.type)),e)}function jt(e,t,n,r){if(50<Jr)throw Jr=0,ii=null,Error(i(185));kr(e,n,r),((fe&2)===0||e!==Be)&&(e===Be&&((fe&2)===0&&(io|=n),Ue===4&&pn(e,Qe)),at(e,r),n===1&&fe===0&&(t.mode&1)===0&&(ir=$e()+500,Ml&&on()))}function at(e,t){var n=e.callbackNode;Nd(e,t);var r=Sl(e,e===Be?Qe:0);if(r===0)n!==null&&Hi(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Hi(n),t===1)e.tag===0?Nf(rc.bind(null,e)):Da(rc.bind(null,e)),vf(function(){(fe&6)===0&&on()}),n=null;else{switch(Yi(r)){case 1:n=Mo;break;case 4:n=Ki;break;case 16:n=yl;break;case 536870912:n=Qi;break;default:n=yl}n=dc(n,nc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function nc(e,t){if(fo=-1,po=0,(fe&6)!==0)throw Error(i(327));var n=e.callbackNode;if(ar()&&e.callbackNode!==n)return null;var r=Sl(e,e===Be?Qe:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=mo(e,r);else{t=r;var l=fe;fe|=2;var o=oc();(Be!==e||Qe!==t)&&(Wt=null,ir=$e()+500,$n(e,t));do try{Bf();break}catch(f){lc(e,f)}while(!0);_s(),so.current=o,fe=l,Re!==null?t=0:(Be=null,Qe=0,t=Ue)}if(t!==0){if(t===2&&(l=Vo(e),l!==0&&(r=l,t=ai(e,l))),t===1)throw n=qr,$n(e,0),pn(e,r),at(e,$e()),n;if(t===6)pn(e,r);else{if(l=e.current.alternate,(r&30)===0&&!Vf(l)&&(t=mo(e,r),t===2&&(o=Vo(e),o!==0&&(r=o,t=ai(e,o))),t===1))throw n=qr,$n(e,0),pn(e,r),at(e,$e()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:An(e,it,Wt);break;case 3:if(pn(e,r),(r&130023424)===r&&(t=oi+500-$e(),10<t)){if(Sl(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){nt(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=ms(An.bind(null,e,it,Wt),t);break}An(e,it,Wt);break;case 4:if(pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var a=31-Nt(r);o=1<<a,a=t[a],a>l&&(l=a),r&=~o}if(r=l,r=$e()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Mf(r/1960))-r,10<r){e.timeoutHandle=ms(An.bind(null,e,it,Wt),r);break}An(e,it,Wt);break;case 5:An(e,it,Wt);break;default:throw Error(i(329))}}}return at(e,$e()),e.callbackNode===n?nc.bind(null,e):null}function ai(e,t){var n=Zr;return e.current.memoizedState.isDehydrated&&($n(e,t).flags|=256),e=mo(e,t),e!==2&&(t=it,it=n,t!==null&&ui(t)),e}function ui(e){it===null?it=e:it.push.apply(it,e)}function Vf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!kt(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pn(e,t){for(t&=~li,t&=~io,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Nt(t),r=1<<n;e[n]=-1,t&=~r}}function rc(e){if((fe&6)!==0)throw Error(i(327));ar();var t=Sl(e,0);if((t&1)===0)return at(e,$e()),null;var n=mo(e,t);if(e.tag!==0&&n===2){var r=Vo(e);r!==0&&(t=r,n=ai(e,r))}if(n===1)throw n=qr,$n(e,0),pn(e,t),at(e,$e()),n;if(n===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,An(e,it,Wt),at(e,$e()),null}function ci(e,t){var n=fe;fe|=1;try{return e(t)}finally{fe=n,fe===0&&(ir=$e()+500,Ml&&on())}}function zn(e){dn!==null&&dn.tag===0&&(fe&6)===0&&ar();var t=fe;fe|=1;var n=xt.transition,r=ve;try{if(xt.transition=null,ve=1,e)return e()}finally{ve=r,xt.transition=n,fe=t,(fe&6)===0&&on()}}function di(){mt=sr.current,Pe(sr)}function $n(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,yf(n)),Re!==null)for(n=Re.return;n!==null;){var r=n;switch(ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Il();break;case 3:rr(),Pe(lt),Pe(Ge),As();break;case 5:zs(r);break;case 4:rr();break;case 13:Pe(be);break;case 19:Pe(be);break;case 10:Es(r.type._context);break;case 22:case 23:di()}n=n.return}if(Be=e,Re=e=mn(e.current,null),Qe=mt=t,Ue=0,qr=null,li=io=On=0,it=Zr=null,bn!==null){for(t=0;t<bn.length;t++)if(n=bn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var a=o.next;o.next=l,r.next=a}n.pending=r}bn=null}return e}function lc(e,t){do{var n=Re;try{if(_s(),Xl.current=eo,ql){for(var r=je.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}ql=!1}if(Cn=0,De=Ie=je=null,Hr=!1,Kr=0,ri.current=null,n===null||n.return===null){Ue=1,qr=t,Re=null;break}e:{var o=e,a=n.return,f=n,p=t;if(t=Qe,f.flags|=32768,p!==null&&typeof p=="object"&&typeof p.then=="function"){var S=p,C=f,z=C.tag;if((C.mode&1)===0&&(z===0||z===11||z===15)){var j=C.alternate;j?(C.updateQueue=j.updateQueue,C.memoizedState=j.memoizedState,C.lanes=j.lanes):(C.updateQueue=null,C.memoizedState=null)}var U=Cu(a);if(U!==null){U.flags&=-257,Ou(U,a,f,o,t),U.mode&1&&ju(o,S,t),t=U,p=S;var D=t.updateQueue;if(D===null){var B=new Set;B.add(p),t.updateQueue=B}else D.add(p);break e}else{if((t&1)===0){ju(o,S,t),fi();break e}p=Error(i(426))}}else if(Ee&&f.mode&1){var Ae=Cu(a);if(Ae!==null){(Ae.flags&65536)===0&&(Ae.flags|=256),Ou(Ae,a,f,o,t),ks(lr(p,f));break e}}o=p=lr(p,f),Ue!==4&&(Ue=2),Zr===null?Zr=[o]:Zr.push(o),o=a;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var x=Eu(o,p,t);Ja(o,x);break e;case 1:f=p;var h=o.type,w=o.stateNode;if((o.flags&128)===0&&(typeof h.getDerivedStateFromError=="function"||w!==null&&typeof w.componentDidCatch=="function"&&(cn===null||!cn.has(w)))){o.flags|=65536,t&=-t,o.lanes|=t;var A=bu(o,f,t);Ja(o,A);break e}}o=o.return}while(o!==null)}ic(n)}catch(W){t=W,Re===n&&n!==null&&(Re=n=n.return);continue}break}while(!0)}function oc(){var e=so.current;return so.current=eo,e===null?eo:e}function fi(){(Ue===0||Ue===3||Ue===2)&&(Ue=4),Be===null||(On&268435455)===0&&(io&268435455)===0||pn(Be,Qe)}function mo(e,t){var n=fe;fe|=2;var r=oc();(Be!==e||Qe!==t)&&(Wt=null,$n(e,t));do try{Df();break}catch(l){lc(e,l)}while(!0);if(_s(),fe=n,so.current=r,Re!==null)throw Error(i(261));return Be=null,Qe=0,Ue}function Df(){for(;Re!==null;)sc(Re)}function Bf(){for(;Re!==null&&!pd();)sc(Re)}function sc(e){var t=cc(e.alternate,e,mt);e.memoizedProps=e.pendingProps,t===null?ic(e):Re=t,ri.current=null}function ic(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=Lf(n,t,mt),n!==null){Re=n;return}}else{if(n=Rf(n,t),n!==null){n.flags&=32767,Re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ue=6,Re=null;return}}if(t=t.sibling,t!==null){Re=t;return}Re=t=e}while(t!==null);Ue===0&&(Ue=5)}function An(e,t,n){var r=ve,l=xt.transition;try{xt.transition=null,ve=1,Wf(e,t,n,r)}finally{xt.transition=l,ve=r}return null}function Wf(e,t,n,r){do ar();while(dn!==null);if((fe&6)!==0)throw Error(i(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(kd(e,o),e===Be&&(Re=Be=null,Qe=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||uo||(uo=!0,dc(yl,function(){return ar(),null})),o=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||o){o=xt.transition,xt.transition=null;var a=ve;ve=1;var f=fe;fe|=4,ri.current=null,If(e,n),qu(n,e),cf(fs),Pl=!!ds,fs=ds=null,e.current=n,Uf(n),md(),fe=f,ve=a,xt.transition=o}else e.current=n;if(uo&&(uo=!1,dn=e,co=l),o=e.pendingLanes,o===0&&(cn=null),yd(n.stateNode),at(e,$e()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(ao)throw ao=!1,e=si,si=null,e;return(co&1)!==0&&e.tag!==0&&ar(),o=e.pendingLanes,(o&1)!==0?e===ii?Jr++:(Jr=0,ii=e):Jr=0,on(),null}function ar(){if(dn!==null){var e=Yi(co),t=xt.transition,n=ve;try{if(xt.transition=null,ve=16>e?16:e,dn===null)var r=!1;else{if(e=dn,dn=null,co=0,(fe&6)!==0)throw Error(i(331));var l=fe;for(fe|=4,V=e.current;V!==null;){var o=V,a=o.child;if((V.flags&16)!==0){var f=o.deletions;if(f!==null){for(var p=0;p<f.length;p++){var S=f[p];for(V=S;V!==null;){var C=V;switch(C.tag){case 0:case 11:case 15:Xr(8,C,o)}var z=C.child;if(z!==null)z.return=C,V=z;else for(;V!==null;){C=V;var j=C.sibling,U=C.return;if(Ku(C),C===S){V=null;break}if(j!==null){j.return=U,V=j;break}V=U}}}var D=o.alternate;if(D!==null){var B=D.child;if(B!==null){D.child=null;do{var Ae=B.sibling;B.sibling=null,B=Ae}while(B!==null)}}V=o}}if((o.subtreeFlags&2064)!==0&&a!==null)a.return=o,V=a;else e:for(;V!==null;){if(o=V,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:Xr(9,o,o.return)}var x=o.sibling;if(x!==null){x.return=o.return,V=x;break e}V=o.return}}var h=e.current;for(V=h;V!==null;){a=V;var w=a.child;if((a.subtreeFlags&2064)!==0&&w!==null)w.return=a,V=w;else e:for(a=h;V!==null;){if(f=V,(f.flags&2048)!==0)try{switch(f.tag){case 0:case 11:case 15:oo(9,f)}}catch(W){ze(f,f.return,W)}if(f===a){V=null;break e}var A=f.sibling;if(A!==null){A.return=f.return,V=A;break e}V=f.return}}if(fe=l,on(),Ot&&typeof Ot.onPostCommitFiberRoot=="function")try{Ot.onPostCommitFiberRoot(vl,e)}catch{}r=!0}return r}finally{ve=n,xt.transition=t}}return!1}function ac(e,t,n){t=lr(n,t),t=Eu(e,t,1),e=an(e,t,1),t=nt(),e!==null&&(kr(e,1,t),at(e,t))}function ze(e,t,n){if(e.tag===3)ac(e,e,n);else for(;t!==null;){if(t.tag===3){ac(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(cn===null||!cn.has(r))){e=lr(n,e),e=bu(t,e,1),t=an(t,e,1),e=nt(),t!==null&&(kr(t,1,e),at(t,e));break}}t=t.return}}function Hf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=nt(),e.pingedLanes|=e.suspendedLanes&n,Be===e&&(Qe&n)===n&&(Ue===4||Ue===3&&(Qe&130023424)===Qe&&500>$e()-oi?$n(e,0):li|=n),at(e,t)}function uc(e,t){t===0&&((e.mode&1)===0?t=1:(t=wl,wl<<=1,(wl&130023424)===0&&(wl=4194304)));var n=nt();e=Vt(e,t),e!==null&&(kr(e,t,n),at(e,n))}function Kf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),uc(e,n)}function Qf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}r!==null&&r.delete(t),uc(e,n)}var cc;cc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||lt.current)st=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return st=!1,Tf(e,t,n);st=(e.flags&131072)!==0}else st=!1,Ee&&(t.flags&1048576)!==0&&Ba(t,Dl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ro(e,t),e=t.pendingProps;var l=Xn(t,Ge.current);nr(t,n),l=Rs(null,t,r,e,l,n);var o=Fs();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ot(r)?(o=!0,Ul(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Cs(t),l.updater=to,t.stateNode=l,l._reactInternals=t,Bs(t,r,e,n),t=Qs(null,t,r,!0,o,n)):(t.tag=0,Ee&&o&&xs(t),tt(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ro(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Yf(r),e=_t(r,e),l){case 0:t=Ks(null,t,r,e,n);break e;case 1:t=Ru(null,t,r,e,n);break e;case 11:t=zu(null,t,r,e,n);break e;case 14:t=$u(null,t,r,_t(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:_t(r,l),Ks(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:_t(r,l),Ru(e,t,r,l,n);case 3:e:{if(Fu(t),e===null)throw Error(i(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Za(e,t),Gl(t,r,null,n);var a=t.memoizedState;if(r=a.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=lr(Error(i(423)),t),t=Iu(e,t,r,n,l);break e}else if(r!==l){l=lr(Error(i(424)),t),t=Iu(e,t,r,n,l);break e}else for(pt=nn(t.stateNode.containerInfo.firstChild),ft=t,Ee=!0,Pt=null,n=Xa(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Jn(),r===l){t=Bt(e,t,n);break e}tt(e,t,r,n)}t=t.child}return t;case 5:return tu(t),e===null&&Ns(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,a=l.children,ps(r,l)?a=null:o!==null&&ps(r,o)&&(t.flags|=32),Lu(e,t),tt(e,t,a,n),t.child;case 6:return e===null&&Ns(t),null;case 13:return Uu(e,t,n);case 4:return Os(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=er(t,null,r,n):tt(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:_t(r,l),zu(e,t,r,l,n);case 7:return tt(e,t,t.pendingProps,n),t.child;case 8:return tt(e,t,t.pendingProps.children,n),t.child;case 12:return tt(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,a=l.value,Ne(Hl,r._currentValue),r._currentValue=a,o!==null)if(kt(o.value,a)){if(o.children===l.children&&!lt.current){t=Bt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var f=o.dependencies;if(f!==null){a=o.child;for(var p=f.firstContext;p!==null;){if(p.context===r){if(o.tag===1){p=Dt(-1,n&-n),p.tag=2;var S=o.updateQueue;if(S!==null){S=S.shared;var C=S.pending;C===null?p.next=p:(p.next=C.next,C.next=p),S.pending=p}}o.lanes|=n,p=o.alternate,p!==null&&(p.lanes|=n),bs(o.return,n,t),f.lanes|=n;break}p=p.next}}else if(o.tag===10)a=o.type===t.type?null:o.child;else if(o.tag===18){if(a=o.return,a===null)throw Error(i(341));a.lanes|=n,f=a.alternate,f!==null&&(f.lanes|=n),bs(a,n,t),a=o.sibling}else a=o.child;if(a!==null)a.return=o;else for(a=o;a!==null;){if(a===t){a=null;break}if(o=a.sibling,o!==null){o.return=a.return,a=o;break}a=a.return}o=a}tt(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,nr(t,n),l=yt(l),r=r(l),t.flags|=1,tt(e,t,r,n),t.child;case 14:return r=t.type,l=_t(r,t.pendingProps),l=_t(r.type,l),$u(e,t,r,l,n);case 15:return Au(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:_t(r,l),ro(e,t),t.tag=1,ot(r)?(e=!0,Ul(t)):e=!1,nr(t,n),Pu(t,r,l),Bs(t,r,l,n),Qs(null,t,r,!0,e,n);case 19:return Vu(e,t,n);case 22:return Tu(e,t,n)}throw Error(i(156,t.tag))};function dc(e,t){return Wi(e,t)}function Gf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wt(e,t,n,r){return new Gf(e,t,n,r)}function pi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Yf(e){if(typeof e=="function")return pi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===oe)return 11;if(e===Je)return 14}return 2}function mn(e,t){var n=e.alternate;return n===null?(n=wt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ho(e,t,n,r,l,o){var a=2;if(r=e,typeof e=="function")pi(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case ye:return Tn(n.children,l,o,t);case He:a=8,l|=8;break;case Ze:return e=wt(12,n,t,l|2),e.elementType=Ze,e.lanes=o,e;case Ve:return e=wt(13,n,t,l),e.elementType=Ve,e.lanes=o,e;case Le:return e=wt(19,n,t,l),e.elementType=Le,e.lanes=o,e;case xe:return go(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Me:a=10;break e;case rt:a=9;break e;case oe:a=11;break e;case Je:a=14;break e;case Oe:a=16,r=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return t=wt(a,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Tn(e,t,n,r){return e=wt(7,e,r,t),e.lanes=n,e}function go(e,t,n,r){return e=wt(22,e,r,t),e.elementType=xe,e.lanes=n,e.stateNode={isHidden:!1},e}function mi(e,t,n){return e=wt(6,e,null,t),e.lanes=n,e}function hi(e,t,n){return t=wt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Xf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Do(0),this.expirationTimes=Do(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Do(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function gi(e,t,n,r,l,o,a,f,p){return e=new Xf(e,t,n,f,p),t===1?(t=1,o===!0&&(t|=8)):t=0,o=wt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Cs(o),e}function qf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Z,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function fc(e){if(!e)return ln;e=e._reactInternals;e:{if(Nn(e)!==e||e.tag!==1)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ot(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(i(171))}if(e.tag===1){var n=e.type;if(ot(n))return Ma(e,n,t)}return t}function pc(e,t,n,r,l,o,a,f,p){return e=gi(n,r,!0,e,l,o,a,f,p),e.context=fc(null),n=e.current,r=nt(),l=fn(n),o=Dt(r,l),o.callback=t??null,an(n,o,l),e.current.lanes=l,kr(e,l,r),at(e,r),e}function yo(e,t,n,r){var l=t.current,o=nt(),a=fn(l);return n=fc(n),t.context===null?t.context=n:t.pendingContext=n,t=Dt(o,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=an(l,t,a),e!==null&&(jt(e,l,a,o),Ql(e,l,a)),a}function vo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function mc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function yi(e,t){mc(e,t),(e=e.alternate)&&mc(e,t)}function Zf(){return null}var hc=typeof reportError=="function"?reportError:function(e){console.error(e)};function vi(e){this._internalRoot=e}xo.prototype.render=vi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));yo(e,t,null,null)},xo.prototype.unmount=vi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zn(function(){yo(null,e,null,null)}),t[Ft]=null}};function xo(e){this._internalRoot=e}xo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zi();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Jt.length&&t!==0&&t<Jt[n].priority;n++);Jt.splice(n,0,e),n===0&&ta(e)}};function xi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function wo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function gc(){}function Jf(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var S=vo(a);o.call(S)}}var a=pc(t,r,e,0,null,!1,!1,"",gc);return e._reactRootContainer=a,e[Ft]=a.current,Fr(e.nodeType===8?e.parentNode:e),zn(),a}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var f=r;r=function(){var S=vo(p);f.call(S)}}var p=gi(e,0,!1,null,null,!1,!1,"",gc);return e._reactRootContainer=p,e[Ft]=p.current,Fr(e.nodeType===8?e.parentNode:e),zn(function(){yo(t,p,n,r)}),p}function So(e,t,n,r,l){var o=n._reactRootContainer;if(o){var a=o;if(typeof l=="function"){var f=l;l=function(){var p=vo(a);f.call(p)}}yo(t,a,e,l)}else a=Jf(n,t,e,l,r);return vo(a)}Xi=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Nr(t.pendingLanes);n!==0&&(Bo(t,n|1),at(t,$e()),(fe&6)===0&&(ir=$e()+500,on()))}break;case 13:zn(function(){var r=Vt(e,1);if(r!==null){var l=nt();jt(r,e,1,l)}}),yi(e,1)}},Wo=function(e){if(e.tag===13){var t=Vt(e,134217728);if(t!==null){var n=nt();jt(t,e,134217728,n)}yi(e,134217728)}},qi=function(e){if(e.tag===13){var t=fn(e),n=Vt(e,t);if(n!==null){var r=nt();jt(n,e,t,r)}yi(e,t)}},Zi=function(){return ve},Ji=function(e,t){var n=ve;try{return ve=e,t()}finally{ve=n}},Ro=function(e,t,n){switch(t){case"input":if(gr(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Fl(r);if(!l)throw Error(i(90));mr(r),gr(r,l)}}}break;case"textarea":de(e,n);break;case"select":t=n.value,t!=null&&$(e,!!n.multiple,t,!1)}},Fi=ci,Ii=zn;var ep={usingClientEntryPoint:!1,Events:[Mr,Gn,Fl,Li,Ri,ci]},el={findFiberByHostInstance:kn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},tp={bundleType:el.bundleType,version:el.version,rendererPackageName:el.rendererPackageName,rendererConfig:el.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:me.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Di(e),e===null?null:e.stateNode},findFiberByHostInstance:el.findFiberByHostInstance||Zf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var No=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!No.isDisabled&&No.supportsFiber)try{vl=No.inject(tp),Ot=No}catch{}}return ut.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ep,ut.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xi(t))throw Error(i(200));return qf(e,t,null,n)},ut.createRoot=function(e,t){if(!xi(e))throw Error(i(299));var n=!1,r="",l=hc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=gi(e,1,!1,null,null,n,!1,r,l),e[Ft]=t.current,Fr(e.nodeType===8?e.parentNode:e),new vi(t)},ut.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=Di(t),e=e===null?null:e.stateNode,e},ut.flushSync=function(e){return zn(e)},ut.hydrate=function(e,t,n){if(!wo(t))throw Error(i(200));return So(null,e,t,!0,n)},ut.hydrateRoot=function(e,t,n){if(!xi(e))throw Error(i(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",a=hc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=pc(t,null,e,1,n??null,l,!1,o,a),e[Ft]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new xo(t)},ut.render=function(e,t,n){if(!wo(t))throw Error(i(200));return So(null,e,t,!1,n)},ut.unmountComponentAtNode=function(e){if(!wo(e))throw Error(i(40));return e._reactRootContainer?(zn(function(){So(null,null,e,!1,function(){e._reactRootContainer=null,e[Ft]=null})}),!0):!1},ut.unstable_batchedUpdates=ci,ut.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!wo(n))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return So(e,t,n,!1,r)},ut.version="18.3.1-next-f1338f8080-20240426",ut}var Pc;function up(){if(Pc)return Ni.exports;Pc=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(u){console.error(u)}}return s(),Ni.exports=ap(),Ni.exports}var _c;function cp(){if(_c)return ko;_c=1;var s=up();return ko.createRoot=s.createRoot,ko.hydrateRoot=s.hydrateRoot,ko}var dp=cp(),ae=zi();function Xc(s){var u,i,y="";if(typeof s=="string"||typeof s=="number")y+=s;else if(typeof s=="object")if(Array.isArray(s)){var N=s.length;for(u=0;u<N;u++)s[u]&&(i=Xc(s[u]))&&(y&&(y+=" "),y+=i)}else for(i in s)s[i]&&(y&&(y+=" "),y+=i);return y}function fp(){for(var s,u,i=0,y="",N=arguments.length;i<N;i++)(s=arguments[i])&&(u=Xc(s))&&(y&&(y+=" "),y+=u);return y}const $i="-",pp=s=>{const u=hp(s),{conflictingClassGroups:i,conflictingClassGroupModifiers:y}=s;return{getClassGroupId:E=>{const P=E.split($i);return P[0]===""&&P.length!==1&&P.shift(),qc(P,u)||mp(E)},getConflictingClassGroupIds:(E,P)=>{const _=i[E]||[];return P&&y[E]?[..._,...y[E]]:_}}},qc=(s,u)=>{var E;if(s.length===0)return u.classGroupId;const i=s[0],y=u.nextPart.get(i),N=y?qc(s.slice(1),y):void 0;if(N)return N;if(u.validators.length===0)return;const k=s.join($i);return(E=u.validators.find(({validator:P})=>P(k)))==null?void 0:E.classGroupId},Ec=/^\[(.+)\]$/,mp=s=>{if(Ec.test(s)){const u=Ec.exec(s)[1],i=u==null?void 0:u.substring(0,u.indexOf(":"));if(i)return"arbitrary.."+i}},hp=s=>{const{theme:u,prefix:i}=s,y={nextPart:new Map,validators:[]};return yp(Object.entries(s.classGroups),i).forEach(([k,E])=>{Oi(E,y,k,u)}),y},Oi=(s,u,i,y)=>{s.forEach(N=>{if(typeof N=="string"){const k=N===""?u:bc(u,N);k.classGroupId=i;return}if(typeof N=="function"){if(gp(N)){Oi(N(y),u,i,y);return}u.validators.push({validator:N,classGroupId:i});return}Object.entries(N).forEach(([k,E])=>{Oi(E,bc(u,k),i,y)})})},bc=(s,u)=>{let i=s;return u.split($i).forEach(y=>{i.nextPart.has(y)||i.nextPart.set(y,{nextPart:new Map,validators:[]}),i=i.nextPart.get(y)}),i},gp=s=>s.isThemeGetter,yp=(s,u)=>u?s.map(([i,y])=>{const N=y.map(k=>typeof k=="string"?u+k:typeof k=="object"?Object.fromEntries(Object.entries(k).map(([E,P])=>[u+E,P])):k);return[i,N]}):s,vp=s=>{if(s<1)return{get:()=>{},set:()=>{}};let u=0,i=new Map,y=new Map;const N=(k,E)=>{i.set(k,E),u++,u>s&&(u=0,y=i,i=new Map)};return{get(k){let E=i.get(k);if(E!==void 0)return E;if((E=y.get(k))!==void 0)return N(k,E),E},set(k,E){i.has(k)?i.set(k,E):N(k,E)}}},Zc="!",xp=s=>{const{separator:u,experimentalParseClassName:i}=s,y=u.length===1,N=u[0],k=u.length,E=P=>{const _=[];let T=0,J=0,X;for(let H=0;H<P.length;H++){let re=P[H];if(T===0){if(re===N&&(y||P.slice(H,H+k)===u)){_.push(P.slice(J,H)),J=H+k;continue}if(re==="/"){X=H;continue}}re==="["?T++:re==="]"&&T--}const Y=_.length===0?P:P.substring(J),ue=Y.startsWith(Zc),ce=ue?Y.substring(1):Y,R=X&&X>J?X-J:void 0;return{modifiers:_,hasImportantModifier:ue,baseClassName:ce,maybePostfixModifierPosition:R}};return i?P=>i({className:P,parseClassName:E}):E},wp=s=>{if(s.length<=1)return s;const u=[];let i=[];return s.forEach(y=>{y[0]==="["?(u.push(...i.sort(),y),i=[]):i.push(y)}),u.push(...i.sort()),u},Sp=s=>({cache:vp(s.cacheSize),parseClassName:xp(s),...pp(s)}),Np=/\s+/,kp=(s,u)=>{const{parseClassName:i,getClassGroupId:y,getConflictingClassGroupIds:N}=u,k=[],E=s.trim().split(Np);let P="";for(let _=E.length-1;_>=0;_-=1){const T=E[_],{modifiers:J,hasImportantModifier:X,baseClassName:Y,maybePostfixModifierPosition:ue}=i(T);let ce=!!ue,R=y(ce?Y.substring(0,ue):Y);if(!R){if(!ce){P=T+(P.length>0?" "+P:P);continue}if(R=y(Y),!R){P=T+(P.length>0?" "+P:P);continue}ce=!1}const H=wp(J).join(":"),re=X?H+Zc:H,ge=re+R;if(k.includes(ge))continue;k.push(ge);const Ce=N(R,ce);for(let me=0;me<Ce.length;++me){const Te=Ce[me];k.push(re+Te)}P=T+(P.length>0?" "+P:P)}return P};function Pp(){let s=0,u,i,y="";for(;s<arguments.length;)(u=arguments[s++])&&(i=Jc(u))&&(y&&(y+=" "),y+=i);return y}const Jc=s=>{if(typeof s=="string")return s;let u,i="";for(let y=0;y<s.length;y++)s[y]&&(u=Jc(s[y]))&&(i&&(i+=" "),i+=u);return i};function _p(s,...u){let i,y,N,k=E;function E(_){const T=u.reduce((J,X)=>X(J),s());return i=Sp(T),y=i.cache.get,N=i.cache.set,k=P,P(_)}function P(_){const T=y(_);if(T)return T;const J=kp(_,i);return N(_,J),J}return function(){return k(Pp.apply(null,arguments))}}const _e=s=>{const u=i=>i[s]||[];return u.isThemeGetter=!0,u},ed=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ep=/^\d+\/\d+$/,bp=new Set(["px","full","screen"]),jp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Cp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Op=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,zp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$p=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ht=s=>fr(s)||bp.has(s)||Ep.test(s),gn=s=>pr(s,"length",Mp),fr=s=>!!s&&!Number.isNaN(Number(s)),_i=s=>pr(s,"number",fr),nl=s=>!!s&&Number.isInteger(Number(s)),Ap=s=>s.endsWith("%")&&fr(s.slice(0,-1)),ne=s=>ed.test(s),yn=s=>jp.test(s),Tp=new Set(["length","size","percentage"]),Lp=s=>pr(s,Tp,td),Rp=s=>pr(s,"position",td),Fp=new Set(["image","url"]),Ip=s=>pr(s,Fp,Dp),Up=s=>pr(s,"",Vp),rl=()=>!0,pr=(s,u,i)=>{const y=ed.exec(s);return y?y[1]?typeof u=="string"?y[1]===u:u.has(y[1]):i(y[2]):!1},Mp=s=>Cp.test(s)&&!Op.test(s),td=()=>!1,Vp=s=>zp.test(s),Dp=s=>$p.test(s),Bp=()=>{const s=_e("colors"),u=_e("spacing"),i=_e("blur"),y=_e("brightness"),N=_e("borderColor"),k=_e("borderRadius"),E=_e("borderSpacing"),P=_e("borderWidth"),_=_e("contrast"),T=_e("grayscale"),J=_e("hueRotate"),X=_e("invert"),Y=_e("gap"),ue=_e("gradientColorStops"),ce=_e("gradientColorStopPositions"),R=_e("inset"),H=_e("margin"),re=_e("opacity"),ge=_e("padding"),Ce=_e("saturate"),me=_e("scale"),Te=_e("sepia"),Z=_e("skew"),ye=_e("space"),He=_e("translate"),Ze=()=>["auto","contain","none"],Me=()=>["auto","hidden","clip","visible","scroll"],rt=()=>["auto",ne,u],oe=()=>[ne,u],Ve=()=>["",Ht,gn],Le=()=>["auto",fr,ne],Je=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Oe=()=>["solid","dashed","dotted","double","none"],xe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],L=()=>["start","end","center","between","around","evenly","stretch"],K=()=>["","0",ne],I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],g=()=>[fr,ne];return{cacheSize:500,separator:":",theme:{colors:[rl],spacing:[Ht,gn],blur:["none","",yn,ne],brightness:g(),borderColor:[s],borderRadius:["none","","full",yn,ne],borderSpacing:oe(),borderWidth:Ve(),contrast:g(),grayscale:K(),hueRotate:g(),invert:K(),gap:oe(),gradientColorStops:[s],gradientColorStopPositions:[Ap,gn],inset:rt(),margin:rt(),opacity:g(),padding:oe(),saturate:g(),scale:g(),sepia:K(),skew:g(),space:oe(),translate:oe()},classGroups:{aspect:[{aspect:["auto","square","video",ne]}],container:["container"],columns:[{columns:[yn]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Je(),ne]}],overflow:[{overflow:Me()}],"overflow-x":[{"overflow-x":Me()}],"overflow-y":[{"overflow-y":Me()}],overscroll:[{overscroll:Ze()}],"overscroll-x":[{"overscroll-x":Ze()}],"overscroll-y":[{"overscroll-y":Ze()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[R]}],"inset-x":[{"inset-x":[R]}],"inset-y":[{"inset-y":[R]}],start:[{start:[R]}],end:[{end:[R]}],top:[{top:[R]}],right:[{right:[R]}],bottom:[{bottom:[R]}],left:[{left:[R]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",nl,ne]}],basis:[{basis:rt()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",ne]}],grow:[{grow:K()}],shrink:[{shrink:K()}],order:[{order:["first","last","none",nl,ne]}],"grid-cols":[{"grid-cols":[rl]}],"col-start-end":[{col:["auto",{span:["full",nl,ne]},ne]}],"col-start":[{"col-start":Le()}],"col-end":[{"col-end":Le()}],"grid-rows":[{"grid-rows":[rl]}],"row-start-end":[{row:["auto",{span:[nl,ne]},ne]}],"row-start":[{"row-start":Le()}],"row-end":[{"row-end":Le()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",ne]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",ne]}],gap:[{gap:[Y]}],"gap-x":[{"gap-x":[Y]}],"gap-y":[{"gap-y":[Y]}],"justify-content":[{justify:["normal",...L()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...L(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...L(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[ge]}],px:[{px:[ge]}],py:[{py:[ge]}],ps:[{ps:[ge]}],pe:[{pe:[ge]}],pt:[{pt:[ge]}],pr:[{pr:[ge]}],pb:[{pb:[ge]}],pl:[{pl:[ge]}],m:[{m:[H]}],mx:[{mx:[H]}],my:[{my:[H]}],ms:[{ms:[H]}],me:[{me:[H]}],mt:[{mt:[H]}],mr:[{mr:[H]}],mb:[{mb:[H]}],ml:[{ml:[H]}],"space-x":[{"space-x":[ye]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ye]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",ne,u]}],"min-w":[{"min-w":[ne,u,"min","max","fit"]}],"max-w":[{"max-w":[ne,u,"none","full","min","max","fit","prose",{screen:[yn]},yn]}],h:[{h:[ne,u,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[ne,u,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[ne,u,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[ne,u,"auto","min","max","fit"]}],"font-size":[{text:["base",yn,gn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",_i]}],"font-family":[{font:[rl]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",ne]}],"line-clamp":[{"line-clamp":["none",fr,_i]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ht,ne]}],"list-image":[{"list-image":["none",ne]}],"list-style-type":[{list:["none","disc","decimal",ne]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[s]}],"placeholder-opacity":[{"placeholder-opacity":[re]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[s]}],"text-opacity":[{"text-opacity":[re]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Oe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ht,gn]}],"underline-offset":[{"underline-offset":["auto",Ht,ne]}],"text-decoration-color":[{decoration:[s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:oe()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[re]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Je(),Rp]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Lp]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Ip]}],"bg-color":[{bg:[s]}],"gradient-from-pos":[{from:[ce]}],"gradient-via-pos":[{via:[ce]}],"gradient-to-pos":[{to:[ce]}],"gradient-from":[{from:[ue]}],"gradient-via":[{via:[ue]}],"gradient-to":[{to:[ue]}],rounded:[{rounded:[k]}],"rounded-s":[{"rounded-s":[k]}],"rounded-e":[{"rounded-e":[k]}],"rounded-t":[{"rounded-t":[k]}],"rounded-r":[{"rounded-r":[k]}],"rounded-b":[{"rounded-b":[k]}],"rounded-l":[{"rounded-l":[k]}],"rounded-ss":[{"rounded-ss":[k]}],"rounded-se":[{"rounded-se":[k]}],"rounded-ee":[{"rounded-ee":[k]}],"rounded-es":[{"rounded-es":[k]}],"rounded-tl":[{"rounded-tl":[k]}],"rounded-tr":[{"rounded-tr":[k]}],"rounded-br":[{"rounded-br":[k]}],"rounded-bl":[{"rounded-bl":[k]}],"border-w":[{border:[P]}],"border-w-x":[{"border-x":[P]}],"border-w-y":[{"border-y":[P]}],"border-w-s":[{"border-s":[P]}],"border-w-e":[{"border-e":[P]}],"border-w-t":[{"border-t":[P]}],"border-w-r":[{"border-r":[P]}],"border-w-b":[{"border-b":[P]}],"border-w-l":[{"border-l":[P]}],"border-opacity":[{"border-opacity":[re]}],"border-style":[{border:[...Oe(),"hidden"]}],"divide-x":[{"divide-x":[P]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[P]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[re]}],"divide-style":[{divide:Oe()}],"border-color":[{border:[N]}],"border-color-x":[{"border-x":[N]}],"border-color-y":[{"border-y":[N]}],"border-color-s":[{"border-s":[N]}],"border-color-e":[{"border-e":[N]}],"border-color-t":[{"border-t":[N]}],"border-color-r":[{"border-r":[N]}],"border-color-b":[{"border-b":[N]}],"border-color-l":[{"border-l":[N]}],"divide-color":[{divide:[N]}],"outline-style":[{outline:["",...Oe()]}],"outline-offset":[{"outline-offset":[Ht,ne]}],"outline-w":[{outline:[Ht,gn]}],"outline-color":[{outline:[s]}],"ring-w":[{ring:Ve()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[s]}],"ring-opacity":[{"ring-opacity":[re]}],"ring-offset-w":[{"ring-offset":[Ht,gn]}],"ring-offset-color":[{"ring-offset":[s]}],shadow:[{shadow:["","inner","none",yn,Up]}],"shadow-color":[{shadow:[rl]}],opacity:[{opacity:[re]}],"mix-blend":[{"mix-blend":[...xe(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":xe()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[y]}],contrast:[{contrast:[_]}],"drop-shadow":[{"drop-shadow":["","none",yn,ne]}],grayscale:[{grayscale:[T]}],"hue-rotate":[{"hue-rotate":[J]}],invert:[{invert:[X]}],saturate:[{saturate:[Ce]}],sepia:[{sepia:[Te]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[y]}],"backdrop-contrast":[{"backdrop-contrast":[_]}],"backdrop-grayscale":[{"backdrop-grayscale":[T]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[J]}],"backdrop-invert":[{"backdrop-invert":[X]}],"backdrop-opacity":[{"backdrop-opacity":[re]}],"backdrop-saturate":[{"backdrop-saturate":[Ce]}],"backdrop-sepia":[{"backdrop-sepia":[Te]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[E]}],"border-spacing-x":[{"border-spacing-x":[E]}],"border-spacing-y":[{"border-spacing-y":[E]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",ne]}],duration:[{duration:g()}],ease:[{ease:["linear","in","out","in-out",ne]}],delay:[{delay:g()}],animate:[{animate:["none","spin","ping","pulse","bounce",ne]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[me]}],"scale-x":[{"scale-x":[me]}],"scale-y":[{"scale-y":[me]}],rotate:[{rotate:[nl,ne]}],"translate-x":[{"translate-x":[He]}],"translate-y":[{"translate-y":[He]}],"skew-x":[{"skew-x":[Z]}],"skew-y":[{"skew-y":[Z]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ne]}],accent:[{accent:["auto",s]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne]}],"caret-color":[{caret:[s]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":oe()}],"scroll-mx":[{"scroll-mx":oe()}],"scroll-my":[{"scroll-my":oe()}],"scroll-ms":[{"scroll-ms":oe()}],"scroll-me":[{"scroll-me":oe()}],"scroll-mt":[{"scroll-mt":oe()}],"scroll-mr":[{"scroll-mr":oe()}],"scroll-mb":[{"scroll-mb":oe()}],"scroll-ml":[{"scroll-ml":oe()}],"scroll-p":[{"scroll-p":oe()}],"scroll-px":[{"scroll-px":oe()}],"scroll-py":[{"scroll-py":oe()}],"scroll-ps":[{"scroll-ps":oe()}],"scroll-pe":[{"scroll-pe":oe()}],"scroll-pt":[{"scroll-pt":oe()}],"scroll-pr":[{"scroll-pr":oe()}],"scroll-pb":[{"scroll-pb":oe()}],"scroll-pl":[{"scroll-pl":oe()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne]}],fill:[{fill:[s,"none"]}],"stroke-w":[{stroke:[Ht,gn,_i]}],stroke:[{stroke:[s,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Wp=_p(Bp),Hp=(...s)=>Wp(fp(s));var Kp=Object.defineProperty,Qp=Object.defineProperties,Gp=Object.getOwnPropertyDescriptors,zo=Object.getOwnPropertySymbols,nd=Object.prototype.hasOwnProperty,rd=Object.prototype.propertyIsEnumerable,jc=(s,u,i)=>u in s?Kp(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,Yp=(s,u)=>{for(var i in u||(u={}))nd.call(u,i)&&jc(s,i,u[i]);if(zo)for(var i of zo(u))rd.call(u,i)&&jc(s,i,u[i]);return s},Xp=(s,u)=>Qp(s,Gp(u)),qp=(s,u)=>{var i={};for(var y in s)nd.call(s,y)&&u.indexOf(y)<0&&(i[y]=s[y]);if(s!=null&&zo)for(var y of zo(s))u.indexOf(y)<0&&rd.call(s,y)&&(i[y]=s[y]);return i};function Qt(s){var u=s,{theme:i,variant:y="primary",className:N,disabled:k,children:E}=u,P=qp(u,["theme","variant","className","disabled","children"]);return d.jsx("button",Xp(Yp({className:Hp("py-1 px-4 rounded shadow transition-all",{"bg-blue-500 hover:bg-blue-600 text-white hover:scale-105":y==="primary"&&!k&&i!=="dark","bg-blue-600 hover:bg-blue-700 text-white hover:scale-105":y==="primary"&&!k&&i==="dark","bg-gray-400 text-gray-600 cursor-not-allowed":y==="primary"&&k,"bg-gray-300 hover:bg-gray-400 text-gray-800 hover:scale-105":y==="secondary"&&!k,"bg-gray-100 text-gray-400 cursor-not-allowed":y==="secondary"&&k,"bg-red-600 bg-opacity-80 hover:bg-red-700 hover:bg-opacity-90 text-white hover:scale-105":y==="danger"&&!k&&i!=="dark","bg-red-500 bg-opacity-70 hover:bg-red-700 hover:bg-opacity-90 text-white hover:scale-105":y==="danger"&&!k&&i==="dark","bg-red-300 bg-opacity-80 text-red-100 cursor-not-allowed":y==="danger"&&k},N),disabled:k},P),{children:E}))}var Zp=Object.defineProperty,Cc=Object.getOwnPropertySymbols,Jp=Object.prototype.hasOwnProperty,em=Object.prototype.propertyIsEnumerable,Oc=(s,u,i)=>u in s?Zp(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,tm=(s,u)=>{for(var i in u||(u={}))Jp.call(u,i)&&Oc(s,i,u[i]);if(Cc)for(var i of Cc(u))em.call(u,i)&&Oc(s,i,u[i]);return s};function nm(s,u){return function(y){return d.jsx(ae.Suspense,{fallback:u,children:d.jsx(s,tm({},y))})}}var rm=Object.defineProperty,zc=Object.getOwnPropertySymbols,lm=Object.prototype.hasOwnProperty,om=Object.prototype.propertyIsEnumerable,$c=(s,u,i)=>u in s?rm(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,sm=(s,u)=>{for(var i in u||(u={}))lm.call(u,i)&&$c(s,i,u[i]);if(zc)for(var i of zc(u))om.call(u,i)&&$c(s,i,u[i]);return s};class im extends ae.Component{constructor(){super(...arguments),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(u,i){console.error(u,i)}render(){return this.state.hasError?this.props.fallback:this.props.children}}function am(s,u){return function(y){return d.jsx(im,{fallback:u,children:d.jsx(s,sm({},y))})}}var Fe=(s=>(s.Planner="planner",s.Navigator="navigator",s.Validator="validator",s))(Fe||{}),O=(s=>(s.OpenAI="openai",s.Anthropic="anthropic",s.DeepSeek="deepseek",s.Gemini="gemini",s.Grok="grok",s.Ollama="ollama",s.AzureOpenAI="azure_openai",s.OpenRouter="openrouter",s.Groq="groq",s.Cerebras="cerebras",s.CustomOpenAI="custom_openai",s))(O||{});const Ct={openai:["gpt-4.1","gpt-4.1-mini","gpt-4o","gpt-4o-mini","o4-mini","o3"],anthropic:["claude-sonnet-4-20250514","claude-3-7-sonnet-latest","claude-3-5-sonnet-latest","claude-3-5-haiku-latest"],deepseek:["deepseek-chat","deepseek-reasoner"],gemini:["gemini-2.5-flash","gemini-2.5-pro"],grok:["grok-3","grok-3-fast","grok-3-mini","grok-3-mini-fast"],ollama:["qwen3:14b","falcon3:10b","qwen2.5-coder:14b","mistral-small:24b"],azure_openai:["gpt-4.1","gpt-4.1-mini","gpt-4.1-nano","gpt-4o","gpt-4o-mini","o4-mini","o3"],openrouter:["openai/gpt-4.1","openai/gpt-4.1-mini","openai/o4-mini","openai/gpt-4o-2024-11-20","google/gemini-2.5-flash-preview"],groq:["llama-3.3-70b-versatile"],cerebras:["llama-3.3-70b"]},ld={openai:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},anthropic:{planner:{temperature:.3,topP:.6},navigator:{temperature:.2,topP:.5},validator:{temperature:.1,topP:.5}},gemini:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},grok:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},ollama:{planner:{temperature:.3,topP:.9},navigator:{temperature:.1,topP:.85},validator:{temperature:.1,topP:.8}},azure_openai:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},openrouter:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},groq:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}},cerebras:{planner:{temperature:.7,topP:.9},navigator:{temperature:.3,topP:.85},validator:{temperature:.1,topP:.8}}};var xn=(s=>(s.Local="local",s.Sync="sync",s.Managed="managed",s.Session="session",s))(xn||{}),od=(s=>(s.ExtensionPagesOnly="TRUSTED_CONTEXTS",s.ExtensionPagesAndContentScripts="TRUSTED_AND_UNTRUSTED_CONTEXTS",s))(od||{}),Co=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});const Lt=globalThis.chrome;function Ac(s,u){return Co(this,null,function*(){function i(N){return typeof N=="function"}function y(N){return N instanceof Promise}return i(s)?(y(s),s(u)):s})}let Tc=!1;function Lc(s){if(Lt&&Lt.storage[s]===void 0)throw new Error(`Check your storage permission in manifest.json: ${s} is not defined`)}function dl(s,u,i){var y,N,k,E,P,_;let T=null,J=!1,X=[];const Y=(y=i==null?void 0:i.storageEnum)!=null?y:xn.Local,ue=(N=i==null?void 0:i.liveUpdate)!=null?N:!1,ce=(E=(k=i==null?void 0:i.serialization)==null?void 0:k.serialize)!=null?E:Z=>Z,R=(_=(P=i==null?void 0:i.serialization)==null?void 0:P.deserialize)!=null?_:Z=>Z;Tc===!1&&Y===xn.Session&&(i==null?void 0:i.sessionAccessForContentScripts)===!0&&(Lc(Y),Lt==null||Lt.storage[Y].setAccessLevel({accessLevel:od.ExtensionPagesAndContentScripts}).catch(Z=>{console.warn(Z),console.warn("Please call setAccessLevel into different context, like a background script.")}),Tc=!0);const H=()=>Co(null,null,function*(){var Z;Lc(Y);const ye=yield Lt==null?void 0:Lt.storage[Y].get([s]);return ye&&(Z=R(ye[s]))!=null?Z:u}),re=()=>{X.forEach(Z=>Z())},ge=Z=>Co(null,null,function*(){J||(T=yield H()),T=yield Ac(Z,T),yield Lt==null?void 0:Lt.storage[Y].set({[s]:ce(T)}),re()}),Ce=Z=>(X=[...X,Z],()=>{X=X.filter(ye=>ye!==Z)}),me=()=>T;H().then(Z=>{T=Z,J=!0,re()});function Te(Z){return Co(this,null,function*(){if(Z[s]===void 0)return;const ye=R(Z[s].newValue);T!==ye&&(T=yield Ac(ye,T),re())})}return ue&&(Lt==null||Lt.storage[Y].onChanged.addListener(Te)),{get:H,set:ge,getSnapshot:me,subscribe:Ce}}var um=Object.defineProperty,cm=Object.defineProperties,dm=Object.getOwnPropertyDescriptors,Rc=Object.getOwnPropertySymbols,fm=Object.prototype.hasOwnProperty,pm=Object.prototype.propertyIsEnumerable,Fc=(s,u,i)=>u in s?um(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,dr=(s,u)=>{for(var i in u||(u={}))fm.call(u,i)&&Fc(s,i,u[i]);if(Rc)for(var i of Rc(u))pm.call(u,i)&&Fc(s,i,u[i]);return s},Ic=(s,u)=>cm(s,dm(u)),ll=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});const vn=dl("llm-api-keys",{providers:{}},{storageEnum:xn.Local,liveUpdate:!0});function sd(s){if(s===O.AzureOpenAI||typeof s=="string"&&s.startsWith(`${O.AzureOpenAI}_`))return O.AzureOpenAI;switch(s){case O.OpenAI:case O.Anthropic:case O.DeepSeek:case O.Gemini:case O.Grok:case O.Ollama:case O.OpenRouter:case O.Groq:case O.Cerebras:return s;default:return O.CustomOpenAI}}function Rt(s){switch(s){case O.OpenAI:return"OpenAI";case O.Anthropic:return"Anthropic";case O.DeepSeek:return"DeepSeek";case O.Gemini:return"Gemini";case O.Grok:return"Grok";case O.Ollama:return"Ollama";case O.AzureOpenAI:return"Azure OpenAI";case O.OpenRouter:return"OpenRouter";case O.Groq:return"Groq";case O.Cerebras:return"Cerebras";default:return s}}function Uc(s){switch(s){case O.OpenAI:case O.Anthropic:case O.DeepSeek:case O.Gemini:case O.Grok:case O.OpenRouter:case O.Groq:case O.Cerebras:return{apiKey:"",name:Rt(s),type:s,baseUrl:s===O.OpenRouter?"https://openrouter.ai/api/v1":void 0,modelNames:[...Ct[s]||[]],createdAt:Date.now()};case O.Ollama:return{apiKey:"ollama",name:Rt(O.Ollama),type:O.Ollama,modelNames:Ct[s],baseUrl:"http://localhost:11434",createdAt:Date.now()};case O.AzureOpenAI:return{apiKey:"",name:Rt(O.AzureOpenAI),type:O.AzureOpenAI,baseUrl:"",azureDeploymentNames:[],azureApiVersion:"2024-02-15-preview",createdAt:Date.now()};default:return{apiKey:"",name:Rt(s),type:O.CustomOpenAI,baseUrl:"",modelNames:[],createdAt:Date.now()}}}function mm(s,u){var i;return((i=ld[s])==null?void 0:i[u])||{temperature:.1,topP:.1}}function Mc(s,u){const i=dr({},u);return i.name||(i.name=Rt(s)),i.type||(i.type=sd(s)),i.type===O.AzureOpenAI?(i.azureApiVersion===void 0&&(i.azureApiVersion="2024-02-15-preview"),i.azureDeploymentNames||(i.azureDeploymentNames=[]),Object.prototype.hasOwnProperty.call(i,"modelNames")&&delete i.modelNames):i.modelNames||(i.modelNames=Ct[s]||[]),i.createdAt||(i.createdAt=new Date("03/04/2025").getTime()),i}const Po=Ic(dr({},vn),{setProvider(s,u){return ll(this,null,function*(){var i,y,N,k;if(!s)throw new Error("Provider id cannot be empty");if(u.apiKey===void 0)throw new Error("API key must be provided (can be empty for local models)");const E=u.type||sd(s);if(E===O.AzureOpenAI){if(!((i=u.baseUrl)!=null&&i.trim()))throw new Error("Azure Endpoint (baseUrl) is required");if(!u.azureDeploymentNames||u.azureDeploymentNames.length===0)throw new Error("At least one Azure Deployment Name is required");if(!((y=u.azureApiVersion)!=null&&y.trim()))throw new Error("Azure API Version is required");if(!((N=u.apiKey)!=null&&N.trim()))throw new Error("API Key is required for Azure OpenAI")}else if(E!==O.CustomOpenAI&&E!==O.Ollama&&!((k=u.apiKey)!=null&&k.trim()))throw new Error(`API Key is required for ${Rt(s)}`);E!==O.AzureOpenAI&&(!u.modelNames||u.modelNames.length===0)&&console.warn(`Provider ${s} of type ${E} is being saved without model names.`);const P=dr({apiKey:u.apiKey||"",baseUrl:u.baseUrl,name:u.name||Rt(s),type:E,createdAt:u.createdAt||Date.now()},E===O.AzureOpenAI?{azureDeploymentNames:u.azureDeploymentNames||[],azureApiVersion:u.azureApiVersion}:{modelNames:u.modelNames||[]});console.log(`[llmProviderStore.setProvider] Saving config for ${s}:`,JSON.stringify(P));const _=(yield vn.get())||{providers:{}};yield vn.set({providers:Ic(dr({},_.providers),{[s]:P})})})},getProvider(s){return ll(this,null,function*(){const i=((yield vn.get())||{providers:{}}).providers[s];return i?Mc(s,i):void 0})},removeProvider(s){return ll(this,null,function*(){const u=(yield vn.get())||{providers:{}},i=dr({},u.providers);delete i[s],yield vn.set({providers:i})})},hasProvider(s){return ll(this,null,function*(){const u=(yield vn.get())||{providers:{}};return s in u.providers})},getAllProviders(){return ll(this,null,function*(){const s=yield vn.get(),u=dr({},s.providers);for(const[i,y]of Object.entries(u))u[i]=Mc(i,y);return u})}});var hm=Object.defineProperty,gm=Object.defineProperties,ym=Object.getOwnPropertyDescriptors,Vc=Object.getOwnPropertySymbols,vm=Object.prototype.hasOwnProperty,xm=Object.prototype.propertyIsEnumerable,Dc=(s,u,i)=>u in s?hm(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,Kt=(s,u)=>{for(var i in u||(u={}))vm.call(u,i)&&Dc(s,i,u[i]);if(Vc)for(var i of Vc(u))xm.call(u,i)&&Dc(s,i,u[i]);return s},_o=(s,u)=>gm(s,ym(u)),ur=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});const Ln=dl("agent-models",{agents:{}},{storageEnum:xn.Local,liveUpdate:!0});function wm(s){if(!s.provider||!s.modelName)throw new Error("Provider and model name must be specified")}function Bc(s,u){var i;const y=(i=ld[u])==null?void 0:i[s];return y??{temperature:.1,topP:.1}}const ol=_o(Kt({},Ln),{setAgentModel:(s,u)=>ur(null,null,function*(){wm(u);const i=Bc(s,u.provider),y=_o(Kt({},u),{parameters:Kt(Kt({},i),u.parameters)});yield Ln.set(N=>({agents:_o(Kt({},N.agents),{[s]:y})}))}),getAgentModel:s=>ur(null,null,function*(){const i=(yield Ln.get()).agents[s];if(!i)return;const y=Bc(s,i.provider);return _o(Kt({},i),{parameters:Kt(Kt({},y),i.parameters)})}),resetAgentModel:s=>ur(null,null,function*(){yield Ln.set(u=>{const i=Kt({},u.agents);return delete i[s],{agents:i}})}),hasAgentModel:s=>ur(null,null,function*(){const u=yield Ln.get();return s in u.agents}),getConfiguredAgents:()=>ur(null,null,function*(){const s=yield Ln.get();return Object.keys(s.agents)}),getAllAgentModels:()=>ur(null,null,function*(){return(yield Ln.get()).agents})});var Sm=Object.defineProperty,Nm=Object.defineProperties,km=Object.getOwnPropertyDescriptors,Wc=Object.getOwnPropertySymbols,Pm=Object.prototype.hasOwnProperty,_m=Object.prototype.propertyIsEnumerable,Hc=(s,u,i)=>u in s?Sm(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,sl=(s,u)=>{for(var i in u||(u={}))Pm.call(u,i)&&Hc(s,i,u[i]);if(Wc)for(var i of Wc(u))_m.call(u,i)&&Hc(s,i,u[i]);return s},Em=(s,u)=>Nm(s,km(u)),Ei=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});const cl={maxSteps:100,maxActionsPerStep:5,maxFailures:3,useVision:!1,useVisionForPlanner:!1,planningInterval:3,displayHighlights:!0,minWaitPageLoad:250,replayHistoricalTasks:!1},il=dl("general-settings",cl,{storageEnum:xn.Local,liveUpdate:!0}),bi=Em(sl({},il),{updateSettings(s){return Ei(this,null,function*(){const u=(yield il.get())||cl,i=sl(sl({},u),s);i.useVision&&!i.displayHighlights&&(i.displayHighlights=!0),yield il.set(i)})},getSettings(){return Ei(this,null,function*(){const s=yield il.get();return sl(sl({},cl),s)})},resetToDefaults(){return Ei(this,null,function*(){yield il.set(cl)})}});var bm=Object.defineProperty,jm=Object.defineProperties,Cm=Object.getOwnPropertyDescriptors,Kc=Object.getOwnPropertySymbols,Om=Object.prototype.hasOwnProperty,zm=Object.prototype.propertyIsEnumerable,Qc=(s,u,i)=>u in s?bm(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,ji=(s,u)=>{for(var i in u||(u={}))Om.call(u,i)&&Qc(s,i,u[i]);if(Kc)for(var i of Kc(u))zm.call(u,i)&&Qc(s,i,u[i]);return s},$m=(s,u)=>jm(s,Cm(u)),Rn=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});function Eo(s){return s.trim().toLowerCase().replace(/^https?:\/\//,"")}const Oo={allowList:[],denyList:[],enabled:!0},al=dl("firewall-settings",Oo,{storageEnum:xn.Local,liveUpdate:!0}),cr=$m(ji({},al),{updateFirewall(s){return Rn(this,null,function*(){const u=(yield al.get())||Oo;yield al.set(ji(ji({},u),s))})},getFirewall(){return Rn(this,null,function*(){return(yield al.get())||Oo})},resetToDefaults(){return Rn(this,null,function*(){yield al.set(Oo)})},addToAllowList(s){return Rn(this,null,function*(){const u=Eo(s),i=yield this.getFirewall();if(!i.allowList.includes(u)){const y=i.denyList.filter(N=>N!==u);yield this.updateFirewall({allowList:[...i.allowList,u],denyList:y})}})},removeFromAllowList(s){return Rn(this,null,function*(){const u=Eo(s),i=yield this.getFirewall();yield this.updateFirewall({allowList:i.allowList.filter(y=>y!==u)})})},addToDenyList(s){return Rn(this,null,function*(){const u=Eo(s),i=yield this.getFirewall();if(!i.denyList.includes(u)){const y=i.allowList.filter(N=>N!==u);yield this.updateFirewall({denyList:[...i.denyList,u],allowList:y})}})},removeFromDenyList(s){return Rn(this,null,function*(){const u=Eo(s),i=yield this.getFirewall();yield this.updateFirewall({denyList:i.denyList.filter(y=>y!==u)})})}});var Am=Object.defineProperty,Tm=Object.defineProperties,Lm=Object.getOwnPropertyDescriptors,Gc=Object.getOwnPropertySymbols,Rm=Object.prototype.hasOwnProperty,Fm=Object.prototype.propertyIsEnumerable,Yc=(s,u,i)=>u in s?Am(s,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[u]=i,Im=(s,u)=>{for(var i in u||(u={}))Rm.call(u,i)&&Yc(s,i,u[i]);if(Gc)for(var i of Gc(u))Fm.call(u,i)&&Yc(s,i,u[i]);return s},Um=(s,u)=>Tm(s,Lm(u)),bo=(s,u,i)=>new Promise((y,N)=>{var k=_=>{try{P(i.next(_))}catch(T){N(T)}},E=_=>{try{P(i.throw(_))}catch(T){N(T)}},P=_=>_.done?y(_.value):Promise.resolve(_.value).then(k,E);P((i=i.apply(s,u)).next())});const ul=dl("speech-to-text-model",{speechToTextModel:void 0},{storageEnum:xn.Local,liveUpdate:!0});function Mm(s){if(!s.provider||!s.modelName)throw new Error("Provider and model name must be specified for speech-to-text")}const Ci=Um(Im({},ul),{setSpeechToTextModel:s=>bo(null,null,function*(){Mm(s),yield ul.set({speechToTextModel:s})}),getSpeechToTextModel:()=>bo(null,null,function*(){return(yield ul.get()).speechToTextModel}),resetSpeechToTextModel:()=>bo(null,null,function*(){yield ul.set({speechToTextModel:void 0})}),hasSpeechToTextModel:()=>bo(null,null,function*(){return(yield ul.get()).speechToTextModel!==void 0})}),Vm=({isDarkMode:s=!1})=>{const[u,i]=ae.useState(cl);ae.useEffect(()=>{bi.getSettings().then(i)},[]);const y=async(N,k)=>{i(P=>({...P,[N]:k})),await bi.updateSettings({[N]:k});const E=await bi.getSettings();i(E)};return d.jsx("section",{className:"space-y-6",children:d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-white"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-left text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"General"}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Max Steps per Task"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Step limit per task"})]}),d.jsx("label",{htmlFor:"maxSteps",className:"sr-only",children:"Max Steps per Task"}),d.jsx("input",{id:"maxSteps",type:"number",min:1,max:50,value:u.maxSteps,onChange:N=>y("maxSteps",Number.parseInt(N.target.value,10)),className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Max Actions per Step"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Action limit per step"})]}),d.jsx("label",{htmlFor:"maxActionsPerStep",className:"sr-only",children:"Max Actions per Step"}),d.jsx("input",{id:"maxActionsPerStep",type:"number",min:1,max:50,value:u.maxActionsPerStep,onChange:N=>y("maxActionsPerStep",Number.parseInt(N.target.value,10)),className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Failure Tolerance"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"How many consecutive failures before stopping"})]}),d.jsx("label",{htmlFor:"maxFailures",className:"sr-only",children:"Failure Tolerance"}),d.jsx("input",{id:"maxFailures",type:"number",min:1,max:10,value:u.maxFailures,onChange:N=>y("maxFailures",Number.parseInt(N.target.value,10)),className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Enable Vision"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Use vision capability of LLMs (consumes more tokens for better results)"})]}),d.jsxs("div",{className:"relative inline-flex cursor-pointer items-center",children:[d.jsx("input",{id:"useVision",type:"checkbox",checked:u.useVision,onChange:N=>y("useVision",N.target.checked),className:"peer sr-only"}),d.jsx("label",{htmlFor:"useVision",className:`peer h-6 w-11 rounded-full ${s?"bg-slate-600":"bg-gray-200"} after:absolute after:left-[2px] after:top-[2px] after:size-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300`,children:d.jsx("span",{className:"sr-only",children:"Enable Vision"})})]})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Display Highlights"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Show visual highlights on interactive elements (e.g. buttons, links, etc.)"})]}),d.jsxs("div",{className:"relative inline-flex cursor-pointer items-center",children:[d.jsx("input",{id:"displayHighlights",type:"checkbox",checked:u.displayHighlights,onChange:N=>y("displayHighlights",N.target.checked),className:"peer sr-only"}),d.jsx("label",{htmlFor:"displayHighlights",className:`peer h-6 w-11 rounded-full ${s?"bg-slate-600":"bg-gray-200"} after:absolute after:left-[2px] after:top-[2px] after:size-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300`,children:d.jsx("span",{className:"sr-only",children:"Display Highlights"})})]})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Replanning Frequency"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Reconsider and update the plan every [Number] steps"})]}),d.jsx("label",{htmlFor:"planningInterval",className:"sr-only",children:"Replanning Frequency"}),d.jsx("input",{id:"planningInterval",type:"number",min:1,max:20,value:u.planningInterval,onChange:N=>y("planningInterval",Number.parseInt(N.target.value,10)),className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Page Load Wait Time"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Minimum wait time after page loads (250-5000ms)"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("label",{htmlFor:"minWaitPageLoad",className:"sr-only",children:"Page Load Wait Time"}),d.jsx("input",{id:"minWaitPageLoad",type:"number",min:250,max:5e3,step:50,value:u.minWaitPageLoad,onChange:N=>y("minWaitPageLoad",Number.parseInt(N.target.value,10)),className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`})]})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h3",{className:`text-base font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Replay Historical Tasks( experimental )"}),d.jsx("p",{className:`text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:"Enable storing and replaying of agent step history (experimental, may have issues)"})]}),d.jsxs("div",{className:"relative inline-flex cursor-pointer items-center",children:[d.jsx("input",{id:"replayHistoricalTasks",type:"checkbox",checked:u.replayHistoricalTasks,onChange:N=>y("replayHistoricalTasks",N.target.checked),className:"peer sr-only"}),d.jsx("label",{htmlFor:"replayHistoricalTasks",className:`peer h-6 w-11 rounded-full ${s?"bg-slate-600":"bg-gray-200"} after:absolute after:left-[2px] after:top-[2px] after:size-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300`,children:d.jsx("span",{className:"sr-only",children:"Replay Historical Tasks"})})]})]})]})]})})};function jo(s){return s.startsWith("openai/")?s.startsWith("openai/o"):s.startsWith("o")}const Dm=({isDarkMode:s=!1})=>{const[u,i]=ae.useState({}),[y,N]=ae.useState(new Set),[k,E]=ae.useState(new Set),[P,_]=ae.useState({[Fe.Navigator]:"",[Fe.Planner]:"",[Fe.Validator]:""}),[T,J]=ae.useState({[Fe.Navigator]:{temperature:0,topP:0},[Fe.Planner]:{temperature:0,topP:0},[Fe.Validator]:{temperature:0,topP:0}}),[X,Y]=ae.useState({[Fe.Navigator]:void 0,[Fe.Planner]:void 0,[Fe.Validator]:void 0}),[ue,ce]=ae.useState({}),[R,H]=ae.useState(!1),re=ae.useRef(null),[ge,Ce]=ae.useState({}),[me,Te]=ae.useState({}),[Z,ye]=ae.useState([]),[He,Ze]=ae.useState("");ae.useEffect(()=>{(async()=>{try{const v=await Po.getAllProviders();console.log("allProviders",v);const m=new Set(Object.keys(v));E(m),i(v)}catch(v){console.error("Error loading providers:",v),i({}),E(new Set)}})()},[]),ae.useEffect(()=>{(async()=>{var v,m;try{const $={[Fe.Planner]:"",[Fe.Navigator]:"",[Fe.Validator]:""};for(const M of Object.values(Fe)){const F=await ol.getAgentModel(M);F&&($[M]=`${F.provider}>${F.modelName}`,(((v=F.parameters)==null?void 0:v.temperature)!==void 0||((m=F.parameters)==null?void 0:m.topP)!==void 0)&&J(de=>{var ct,St;return{...de,[M]:{temperature:((ct=F.parameters)==null?void 0:ct.temperature)??de[M].temperature,topP:((St=F.parameters)==null?void 0:St.topP)??de[M].topP}}}),F.reasoningEffort&&Y(de=>({...de,[M]:F.reasoningEffort})))}_($)}catch($){console.error("Error loading agent models:",$)}})()},[]),ae.useEffect(()=>{(async()=>{try{const v=await Ci.getSpeechToTextModel();v&&Ze(`${v.provider}>${v.modelName}`)}catch(v){console.error("Error loading speech-to-text model:",v)}})()},[]),ae.useEffect(()=>{if(re.current&&u[re.current]){const c=re.current;if(u[c].type===O.CustomOpenAI){const m=document.getElementById(`${c}-name`);m&&m.focus()}else{const m=document.getElementById(`${c}-api-key`);m&&m.focus()}re.current=null}},[u]),ae.useEffect(()=>{const c=v=>{const m=v.target;R&&!m.closest(".provider-selector-container")&&H(!1)};return document.addEventListener("mousedown",c),()=>{document.removeEventListener("mousedown",c)}},[R]);const Me=ae.useCallback(async()=>{const c=[];try{const v=await Po.getAllProviders();for(const[m,$]of Object.entries(v))if($.type===O.AzureOpenAI){const M=$.azureDeploymentNames||[];c.push(...M.map(F=>({provider:m,providerName:$.name||m,model:F})))}else{const M=$.modelNames||Ct[m]||[];c.push(...M.map(F=>({provider:m,providerName:$.name||m,model:F})))}}catch(v){console.error("Error loading providers for model selection:",v)}return c},[]);ae.useEffect(()=>{(async()=>{const v=await Me();ye(v)})()},[Me]);const rt=(c,v,m)=>{N($=>new Set($).add(c)),i($=>{var M;return{...$,[c]:{...$[c],apiKey:v.trim(),baseUrl:m!==void 0?m.trim():(M=$[c])==null?void 0:M.baseUrl}}})},oe=c=>{Te(v=>({...v,[c]:!v[c]}))},Ve=(c,v)=>{N(m=>new Set(m).add(c)),i(m=>({...m,[c]:{...m[c],name:v.trim()}}))},Le=(c,v)=>{ce(m=>({...m,[c]:v}))},Je=(c,v)=>{v.trim()&&(N(m=>new Set(m).add(c)),i(m=>{const $=m[c]||{};let M=$.modelNames;return M===void 0&&(M=[...Ct[c]||[]]),M.includes(v.trim())?m:{...m,[c]:{...$,modelNames:[...M,v.trim()]}}}),ce(m=>({...m,[c]:""})))},Oe=(c,v)=>{N(m=>new Set(m).add(c)),i(m=>{const $=m[c]||{};if(!$.modelNames){const F=(Ct[c]||[]).filter(de=>de!==v);return{...m,[c]:{...$,modelNames:F}}}return{...m,[c]:{...$,modelNames:$.modelNames.filter(M=>M!==v)}}})},xe=(c,v)=>{if(c.key==="Enter"||c.key===" "){c.preventDefault();const m=ue[v]||"";Je(v,m)}},L=c=>{var de,ct,St,In,Sn,yr,Gt,Yt,ml,vr;const v=k.has(c),m=y.has(c);if(v&&!m)return{theme:s?"dark":"light",variant:"danger",children:"Delete",disabled:!1};let $=!1;const M=(de=u[c])==null?void 0:de.type,F=u[c];return M===O.CustomOpenAI?$=!!((ct=F==null?void 0:F.baseUrl)!=null&&ct.trim()):M===O.Ollama?$=!!((St=F==null?void 0:F.baseUrl)!=null&&St.trim()):M===O.AzureOpenAI?$=!!((In=F==null?void 0:F.apiKey)!=null&&In.trim())&&!!((Sn=F==null?void 0:F.baseUrl)!=null&&Sn.trim())&&!!((yr=F==null?void 0:F.azureDeploymentNames)!=null&&yr.length)&&!!((Gt=F==null?void 0:F.azureApiVersion)!=null&&Gt.trim()):M===O.OpenRouter?$=!!((Yt=F==null?void 0:F.apiKey)!=null&&Yt.trim())&&!!((ml=F==null?void 0:F.baseUrl)!=null&&ml.trim()):$=!!((vr=F==null?void 0:F.apiKey)!=null&&vr.trim()),{theme:s?"dark":"light",variant:"primary",children:"Save",disabled:!$||!m}},K=async c=>{var v;try{if(u[c].type===O.CustomOpenAI&&((v=u[c].name)!=null&&v.includes(" "))){Ce(F=>({...F,[c]:"Spaces are not allowed in provider names. Please use underscores or other characters instead."}));return}if((u[c].type===O.CustomOpenAI||u[c].type===O.Ollama||u[c].type===O.AzureOpenAI||u[c].type===O.OpenRouter)&&(!u[c].baseUrl||!u[c].baseUrl.trim())){alert(`Base URL is required for ${Rt(c)}. Please enter it.`);return}let m=u[c].modelNames;m||(m=[...Ct[c]||[]]);const $={...u[c]};$.apiKey=u[c].apiKey||"",$.name=u[c].name||Rt(c),$.type=u[c].type,$.createdAt=u[c].createdAt||Date.now(),u[c].type===O.AzureOpenAI?$.modelNames=void 0:$.modelNames=u[c].modelNames||Ct[c]||[],await Po.setProvider(c,$),Ce(F=>{const de={...F};return delete de[c],de}),E(F=>new Set(F).add(c)),N(F=>{const de=new Set(F);return de.delete(c),de});const M=await Me();ye(M)}catch(m){console.error("Error saving API key:",m)}},I=async c=>{try{await Po.removeProvider(c),E(m=>{const $=new Set(m);return $.delete(c),$}),i(m=>{const $={...m};return delete $[c],$}),N(m=>{const $=new Set(m);return $.delete(c),$});const v=await Me();ye(v)}catch(v){console.error("Error deleting provider:",v)}},g=c=>{i(v=>{const m={...v};return delete m[c],m}),N(v=>{const m=new Set(v);return m.delete(c),m})},b=async(c,v)=>{const[m,$]=v.split(">");console.log(`[handleModelChange] Setting ${c} model: provider=${m}, model=${$}`);const M=mm(m,c);J(F=>({...F,[c]:M})),_(F=>({...F,[c]:v}));try{if($){const F=u[m];F&&F.type===O.AzureOpenAI&&console.log(`[handleModelChange] Azure model selected: ${$}`),jo($)?Y(de=>({...de,[c]:de[c]||"medium"})):Y(de=>({...de,[c]:void 0})),await ol.setAgentModel(c,{provider:m,modelName:$,parameters:M,reasoningEffort:jo($)?X[c]||"medium":void 0})}else await ol.resetAgentModel(c)}catch(F){console.error("Error saving agent model:",F)}},te=async(c,v)=>{if(Y(m=>({...m,[c]:v})),P[c]&&jo(P[c]))try{const m=hr(P[c]);m&&await ol.setAgentModel(c,{provider:m,modelName:P[c],parameters:T[c],reasoningEffort:v})}catch(m){console.error("Error saving reasoning effort:",m)}},ee=async(c,v,m)=>{const $={...T[c],[v]:m};if(J(M=>({...M,[c]:$})),P[c])try{let M;for(const[F,de]of Object.entries(u))if(de.type===O.AzureOpenAI){if((de.azureDeploymentNames||[]).includes(P[c])){M=F;break}}else if((de.modelNames||Ct[F]||[]).includes(P[c])){M=F;break}M&&await ol.setAgentModel(c,{provider:M,modelName:P[c],parameters:$})}catch(M){console.error("Error saving agent parameters:",M)}},se=async c=>{Ze(c);try{if(c){const[v,m]=c.split(">");await Ci.setSpeechToTextModel({provider:v,modelName:m})}else await Ci.resetSpeechToTextModel()}catch(v){console.error("Error saving speech-to-text model:",v)}},ie=c=>d.jsxs("div",{className:`rounded-lg border ${s?"border-gray-700 bg-slate-800":"border-gray-200 bg-gray-50"} p-4`,children:[d.jsx("h3",{className:`mb-2 text-lg font-medium ${s?"text-gray-300":"text-gray-700"}`,children:c.charAt(0).toUpperCase()+c.slice(1)}),d.jsx("p",{className:`mb-4 text-sm font-normal ${s?"text-gray-400":"text-gray-500"}`,children:we(c)}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-model`,className:`w-24 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Model"}),d.jsxs("select",{id:`${c}-model`,className:`flex-1 rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`,disabled:Z.length===0,value:P[c]||"",onChange:v=>b(c,v.target.value),children:[d.jsx("option",{value:"",children:"Choose model"},"default"),Z.map(({provider:v,providerName:m,model:$})=>d.jsx("option",{value:`${v}>${$}`,children:`${m} > ${$}`},`${v}>${$}`))]})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-temperature`,className:`w-24 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Temperature"}),d.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[d.jsx("input",{id:`${c}-temperature`,type:"range",min:"0",max:"2",step:"0.01",value:T[c].temperature,onChange:v=>ee(c,"temperature",Number.parseFloat(v.target.value)),style:{background:`linear-gradient(to right, ${s?"#3b82f6":"#60a5fa"} 0%, ${s?"#3b82f6":"#60a5fa"} ${T[c].temperature/2*100}%, ${s?"#475569":"#cbd5e1"} ${T[c].temperature/2*100}%, ${s?"#475569":"#cbd5e1"} 100%)`},className:`flex-1 ${s?"accent-blue-500":"accent-blue-400"} h-1 appearance-none rounded-full`}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:`w-12 text-sm ${s?"text-gray-300":"text-gray-600"}`,children:T[c].temperature.toFixed(2)}),d.jsx("input",{type:"number",min:"0",max:"2",step:"0.01",value:T[c].temperature,onChange:v=>{const m=Number.parseFloat(v.target.value);!Number.isNaN(m)&&m>=0&&m<=2&&ee(c,"temperature",m)},className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-800":"border-gray-300 bg-white text-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} px-2 py-1 text-sm`,"aria-label":`${c} temperature number input`})]})]})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-topP`,className:`w-24 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Top P"}),d.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[d.jsx("input",{id:`${c}-topP`,type:"range",min:"0",max:"1",step:"0.001",value:T[c].topP,onChange:v=>ee(c,"topP",Number.parseFloat(v.target.value)),style:{background:`linear-gradient(to right, ${s?"#3b82f6":"#60a5fa"} 0%, ${s?"#3b82f6":"#60a5fa"} ${T[c].topP*100}%, ${s?"#475569":"#cbd5e1"} ${T[c].topP*100}%, ${s?"#475569":"#cbd5e1"} 100%)`},className:`flex-1 ${s?"accent-blue-500":"accent-blue-400"} h-1 appearance-none rounded-full`}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:`w-12 text-sm ${s?"text-gray-300":"text-gray-600"}`,children:T[c].topP.toFixed(3)}),d.jsx("input",{type:"number",min:"0",max:"1",step:"0.001",value:T[c].topP,onChange:v=>{const m=Number.parseFloat(v.target.value);!Number.isNaN(m)&&m>=0&&m<=1&&ee(c,"topP",m)},className:`w-20 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-800":"border-gray-300 bg-white text-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} px-2 py-1 text-sm`,"aria-label":`${c} top P number input`})]})]})]}),P[c]&&jo(P[c])&&d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-reasoning-effort`,className:`w-24 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Reasoning"}),d.jsx("div",{className:"flex flex-1 items-center space-x-2",children:d.jsxs("select",{id:`${c}-reasoning-effort`,value:X[c]||"medium",onChange:v=>te(c,v.target.value),className:`flex-1 rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`,children:[d.jsx("option",{value:"low",children:"Low (Faster)"}),d.jsx("option",{value:"medium",children:"Medium (Balanced)"}),d.jsx("option",{value:"high",children:"High (More thorough)"})]})})]})]})]}),we=c=>{switch(c){case Fe.Navigator:return"Navigates websites and performs actions";case Fe.Planner:return"Develops and refines strategies to complete tasks";case Fe.Validator:return"Checks if tasks are completed successfully";default:return""}},he=()=>{let c=0;for(const v of Object.keys(u))if(v.startsWith("custom_openai_")){const m=v.match(/custom_openai_(\d+)/);if(m){const $=Number.parseInt(m[1],10);c=Math.max(c,$)}}return c},Se=()=>{const c=he()+1,v=`custom_openai_${c}`;i(m=>({...m,[v]:{apiKey:"",name:`CustomProvider${c}`,type:O.CustomOpenAI,baseUrl:"",modelNames:[],createdAt:Date.now()}})),N(m=>new Set(m).add(v)),re.current=v,setTimeout(()=>{const m=document.getElementById(`provider-${v}`);m&&m.scrollIntoView({behavior:"smooth",block:"center"})},100)},et=c=>{const v=Uc(c);i(m=>({...m,[c]:v})),N(m=>new Set(m).add(c)),re.current=c,setTimeout(()=>{const m=document.getElementById(`provider-${c}`);m&&m.scrollIntoView({behavior:"smooth",block:"center"})},100)},wn=()=>Object.entries(u).filter(([v,m])=>!m||!m.type?(console.warn(`Filtering out provider ${v} with missing config or type.`),!1):!!(k.has(v)||y.has(v))).sort(([v,m],[$,M])=>{const F=!k.has(v)&&y.has(v),de=!k.has($)&&y.has($);if(F&&!de)return 1;if(!F&&de)return-1;if(m.createdAt&&M.createdAt)return m.createdAt-M.createdAt;if(m.createdAt)return-1;if(M.createdAt)return 1;const ct=m.type===O.CustomOpenAI,St=M.type===O.CustomOpenAI;return ct&&!St?1:!ct&&St?-1:(m.name||v).localeCompare(M.name||$)}),mr=c=>{if(H(!1),c===O.CustomOpenAI){Se();return}if(c===O.AzureOpenAI){Fn();return}et(c)},Fn=()=>{const v=Object.keys(u).filter(M=>M===O.AzureOpenAI||M.startsWith(`${O.AzureOpenAI}_`)).length+1,m=v===1?O.AzureOpenAI:`${O.AzureOpenAI}_${v}`,$=Uc(O.AzureOpenAI);$.name=`Azure OpenAI ${v}`,i(M=>({...M,[m]:$})),N(M=>new Set(M).add(m)),re.current=m,setTimeout(()=>{const M=document.getElementById(`provider-${m}`);M&&M.scrollIntoView({behavior:"smooth",block:"center"})},100)},hr=c=>{for(const[v,m]of Object.entries(u))if(m.type===O.AzureOpenAI){if((m.azureDeploymentNames||[]).includes(c))return v}else if((m.modelNames||Ct[v]||[]).includes(c))return v;return""},fl=(c,v)=>{v.trim()&&(N(m=>new Set(m).add(c)),i(m=>{const $=m[c]||{},M=$.azureDeploymentNames||[];return M.includes(v.trim())?m:{...m,[c]:{...$,azureDeploymentNames:[...M,v.trim()]}}}),ce(m=>({...m,[c]:""})))},pl=(c,v)=>{N(m=>new Set(m).add(c)),i(m=>{const $=m[c]||{},F=($.azureDeploymentNames||[]).filter(de=>de!==v);return{...m,[c]:{...$,azureDeploymentNames:F}}})},gr=(c,v)=>{N(m=>new Set(m).add(c)),i(m=>({...m,[c]:{...m[c],azureApiVersion:v.trim()}}))};return d.jsxs("section",{className:"space-y-6",children:[d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-gray-50"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"LLM Providers"}),d.jsxs("div",{className:"space-y-6",children:[wn().length===0?d.jsx("div",{className:"py-8 text-center text-gray-500",children:d.jsx("p",{className:"mb-4",children:"No providers configured yet. Add a provider to get started."})}):wn().map(([c,v])=>!v||!v.type?(console.warn(`Skipping rendering for providerId ${c} due to missing config or type`),null):d.jsxs("div",{id:`provider-${c}`,className:`space-y-4 ${y.has(c)&&!k.has(c)?`rounded-lg border p-4 ${s?"border-blue-700 bg-slate-700":"border-blue-200 bg-blue-50/70"}`:""}`,children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("h3",{className:`text-lg font-medium ${s?"text-gray-300":"text-gray-700"}`,children:v.name||c}),d.jsxs("div",{className:"flex space-x-2",children:[y.has(c)&&!k.has(c)&&d.jsx(Qt,{variant:"secondary",onClick:()=>g(c),children:"Cancel"}),d.jsx(Qt,{variant:L(c).variant,disabled:L(c).disabled,onClick:()=>k.has(c)&&!y.has(c)?I(c):K(c),children:L(c).children})]})]}),y.has(c)&&!k.has(c)&&d.jsx("div",{className:`mb-2 text-sm ${s?"text-teal-300":"text-teal-700"}`,children:d.jsx("p",{children:"This provider is newly added. Enter your API key and click Save to configure it."})}),d.jsxs("div",{className:"space-y-3",children:[v.type===O.CustomOpenAI&&d.jsxs("div",{className:"flex flex-col",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-name`,className:`w-20 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Name"}),d.jsx("input",{id:`${c}-name`,type:"text",placeholder:"Provider name",value:v.name||"",onChange:m=>{console.log("Name input changed:",m.target.value),Ve(c,m.target.value)},className:`flex-1 rounded-md border p-2 text-sm ${ge[c]?s?"border-red-700 bg-slate-700 text-gray-200 focus:border-red-600 focus:ring-2 focus:ring-red-900":"border-red-300 bg-gray-50 focus:border-red-400 focus:ring-2 focus:ring-red-200":s?"border-blue-700 bg-slate-700 text-gray-200 focus:border-blue-600 focus:ring-2 focus:ring-blue-900":"border-blue-300 bg-gray-50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} outline-none`})]}),ge[c]?d.jsx("p",{className:`ml-20 mt-1 text-xs ${s?"text-red-400":"text-red-500"}`,children:ge[c]}):d.jsx("p",{className:`ml-20 mt-1 text-xs ${s?"text-gray-400":"text-gray-500"}`,children:"Provider name (spaces are not allowed when saving)"})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsxs("label",{htmlFor:`${c}-api-key`,className:`w-20 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:["API Key",v.type!==O.CustomOpenAI&&v.type!==O.Ollama?"*":""]}),d.jsxs("div",{className:"relative flex-1",children:[d.jsx("input",{id:`${c}-api-key`,type:"password",placeholder:v.type===O.CustomOpenAI?`${v.name||c} API key (optional)`:v.type===O.Ollama?"API Key (leave empty for Ollama)":`${v.name||c} API key (required)`,value:v.apiKey||"",onChange:m=>rt(c,m.target.value,v.baseUrl),className:`w-full rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-800":"border-gray-300 bg-white text-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} p-2 outline-none`}),y.has(c)&&!k.has(c)&&d.jsx("button",{type:"button",className:`absolute right-2 top-1/2 -translate-y-1/2 ${s?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>oe(c),"aria-label":me[c]?"Hide API key":"Show API key",children:d.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"size-5","aria-hidden":"true",children:[d.jsx("title",{children:me[c]?"Hide API key":"Show API key"}),me[c]?d.jsxs(d.Fragment,{children:[d.jsx("path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"}),d.jsx("circle",{cx:"12",cy:"12",r:"3"}),d.jsx("line",{x1:"2",y1:"22",x2:"22",y2:"2"})]}):d.jsxs(d.Fragment,{children:[d.jsx("path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"}),d.jsx("circle",{cx:"12",cy:"12",r:"3"})]})]})})]})]}),y.has(c)&&!k.has(c)&&me[c]&&v.apiKey&&d.jsx("div",{className:"ml-20 mt-1",children:d.jsx("p",{className:`break-words font-mono text-sm ${s?"text-emerald-400":"text-emerald-600"}`,children:v.apiKey})}),(v.type===O.CustomOpenAI||v.type===O.Ollama||v.type===O.AzureOpenAI||v.type===O.OpenRouter)&&d.jsx("div",{className:"flex flex-col",children:d.jsxs("div",{className:"flex items-center",children:[d.jsxs("label",{htmlFor:`${c}-base-url`,className:`w-20 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:[v.type===O.AzureOpenAI?"Endpoint":"Base URL",v.type===O.CustomOpenAI||v.type===O.AzureOpenAI?"*":""]}),d.jsx("input",{id:`${c}-base-url`,type:"text",placeholder:v.type===O.CustomOpenAI?"Required OpenAI-compatible API endpoint":v.type===O.AzureOpenAI?"https://YOUR_RESOURCE_NAME.openai.azure.com/":v.type===O.OpenRouter?"OpenRouter Base URL (optional, defaults to https://openrouter.ai/api/v1)":"Ollama base URL",value:v.baseUrl||"",onChange:m=>rt(c,v.apiKey||"",m.target.value),className:`flex-1 rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-800":"border-gray-300 bg-white text-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} p-2 outline-none`})]})}),v.type===O.AzureOpenAI&&d.jsxs("div",{className:"flex items-start",children:[d.jsx("label",{htmlFor:`${c}-azure-deployment`,className:`w-20 pt-2 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Deployment*"}),d.jsxs("div",{className:"flex-1 space-y-2",children:[d.jsxs("div",{className:`flex min-h-[42px] flex-wrap items-center gap-2 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} p-2`,children:[(v.azureDeploymentNames||[]).length>0?(v.azureDeploymentNames||[]).map(m=>d.jsxs("div",{className:`flex items-center rounded-full ${s?"bg-blue-900 text-blue-100":"bg-blue-100 text-blue-800"} px-2 py-1 text-sm`,children:[d.jsx("span",{children:m}),d.jsx("button",{type:"button",onClick:()=>pl(c,m),className:`ml-1 font-bold ${s?"text-blue-300 hover:text-blue-100":"text-blue-600 hover:text-blue-800"}`,"aria-label":`Remove ${m}`,children:"×"})]},m)):null,d.jsx("input",{id:`${c}-azure-deployment-input`,type:"text",placeholder:"Enter Azure model name (e.g. gpt-4o, gpt-4o-mini)",value:ue[c]||"",onChange:m=>Le(c,m.target.value),onKeyDown:m=>{if(m.key==="Enter"||m.key===" "){m.preventDefault();const $=ue[c]||"";$.trim()&&(fl(c,$.trim()),ce(M=>({...M,[c]:""})))}},className:`min-w-[150px] flex-1 border-none text-sm ${s?"bg-transparent text-gray-200":"bg-transparent text-gray-700"} p-1 outline-none`})]}),d.jsx("p",{className:`mt-1 text-xs ${s?"text-gray-400":"text-gray-500"}`,children:"Type model name and press Enter or Space to set. Deployment name should match OpenAI model name (e.g., gpt-4o) for best compatibility."})]})]}),v.type===O.AzureOpenAI&&d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:`${c}-azure-version`,className:`w-20 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"API Version*"}),d.jsx("input",{id:`${c}-azure-version`,type:"text",placeholder:"e.g., 2024-02-15-preview",value:v.azureApiVersion||"",onChange:m=>gr(c,m.target.value),className:`flex-1 rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-800":"border-gray-300 bg-white text-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"} p-2 outline-none`})]}),v.type!==O.AzureOpenAI&&d.jsxs("div",{className:"flex items-start",children:[d.jsx("label",{htmlFor:`${c}-models-label`,className:`w-20 pt-2 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Models"}),d.jsx("div",{className:"flex-1 space-y-2",children:v.type===O.OpenRouter?d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:`flex min-h-[42px] flex-wrap items-center gap-2 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} p-2`,children:[v.modelNames&&v.modelNames.length>0?v.modelNames.map(m=>d.jsxs("div",{className:`flex items-center rounded-full ${s?"bg-blue-900 text-blue-100":"bg-blue-100 text-blue-800"} px-2 py-1 text-sm`,children:[d.jsx("span",{children:m}),d.jsx("button",{type:"button",onClick:()=>Oe(c,m),className:`ml-1 font-bold ${s?"text-blue-300 hover:text-blue-100":"text-blue-600 hover:text-blue-800"}`,"aria-label":`Remove ${m}`,children:"×"})]},m)):d.jsx("span",{className:`text-xs ${s?"text-gray-400":"text-gray-500"}`,children:"No models selected. Add model names manually if needed."}),d.jsx("input",{id:`${c}-models-input`,type:"text",placeholder:"",value:ue[c]||"",onChange:m=>Le(c,m.target.value),onKeyDown:m=>xe(m,c),className:`min-w-[150px] flex-1 border-none text-sm ${s?"bg-transparent text-gray-200":"bg-transparent text-gray-700"} p-1 outline-none`})]}),d.jsx("p",{className:`mt-1 text-xs ${s?"text-gray-400":"text-gray-500"}`,children:"Type and Press Enter or Space to add."})]}):d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:`flex min-h-[42px] flex-wrap items-center gap-2 rounded-md border ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} p-2`,children:[(v.modelNames!==void 0?v.modelNames:Ct[c]||[]).map($=>d.jsxs("div",{className:`flex items-center rounded-full ${s?"bg-blue-900 text-blue-100":"bg-blue-100 text-blue-800"} px-2 py-1 text-sm`,children:[d.jsx("span",{children:$}),d.jsx("button",{type:"button",onClick:()=>Oe(c,$),className:`ml-1 font-bold ${s?"text-blue-300 hover:text-blue-100":"text-blue-600 hover:text-blue-800"}`,"aria-label":`Remove ${$}`,children:"×"})]},$)),d.jsx("input",{id:`${c}-models-input`,type:"text",placeholder:"",value:ue[c]||"",onChange:m=>Le(c,m.target.value),onKeyDown:m=>xe(m,c),className:`min-w-[150px] flex-1 border-none text-sm ${s?"bg-transparent text-gray-200":"bg-transparent text-gray-700"} p-1 outline-none`})]}),d.jsx("p",{className:`mt-1 text-xs ${s?"text-gray-400":"text-gray-500"}`,children:"Type and Press Enter or Space to add."})]})})]}),v.type===O.Ollama&&d.jsx("div",{className:`mt-4 rounded-md border ${s?"border-slate-600 bg-slate-700":"border-blue-100 bg-blue-50"} p-3`,children:d.jsxs("p",{className:`text-sm ${s?"text-gray-200":"text-gray-700"}`,children:[d.jsx("strong",{children:"Remember:"})," Add"," ",d.jsx("code",{className:`rounded italic ${s?"bg-slate-600 px-1 py-0.5":"bg-blue-100 px-1 py-0.5"}`,children:"OLLAMA_ORIGINS=chrome-extension://*"})," ","environment variable for the Ollama server.",d.jsx("a",{href:"https://github.com/ollama/ollama/blob/main/docs/faq.md#how-can-i-allow-additional-web-origins-to-access-ollama",target:"_blank",rel:"noopener noreferrer",className:`ml-1 ${s?"text-blue-400 hover:text-blue-300":"text-blue-600 hover:text-blue-800"}`,children:"Learn more"})]})})]}),Object.keys(u).indexOf(c)<Object.keys(u).length-1&&d.jsx("div",{className:`mt-4 border-t ${s?"border-gray-700":"border-gray-200"}`})]},c)),d.jsxs("div",{className:"provider-selector-container relative pt-4",children:[d.jsxs(Qt,{variant:"secondary",onClick:()=>H(c=>!c),className:`flex w-full items-center justify-center font-medium ${s?"border-blue-700 bg-blue-600 text-white hover:bg-blue-500":"border-blue-200 bg-blue-100 text-blue-800 hover:bg-blue-200"}`,children:[d.jsx("span",{className:"mr-2 text-sm",children:"+"})," ",d.jsx("span",{className:"text-sm",children:"Add New Provider"})]}),R&&d.jsx("div",{className:`absolute z-10 mt-2 w-full overflow-hidden rounded-md border ${s?"border-blue-600 bg-slate-700 shadow-lg shadow-slate-900/50":"border-blue-200 bg-white shadow-xl shadow-blue-100/50"}`,children:d.jsxs("div",{className:"py-1",children:[Object.values(O).filter(c=>c===O.AzureOpenAI||c!==O.CustomOpenAI&&!k.has(c)&&!y.has(c)).map(c=>d.jsx("button",{type:"button",className:`flex w-full items-center px-4 py-3 text-left text-sm ${s?"text-blue-200 hover:bg-blue-600/30 hover:text-white":"text-blue-700 hover:bg-blue-100 hover:text-blue-800"} transition-colors duration-150`,onClick:()=>mr(c),children:d.jsx("span",{className:"font-medium",children:Rt(c)})},c)),d.jsx("button",{type:"button",className:`flex w-full items-center px-4 py-3 text-left text-sm ${s?"text-blue-200 hover:bg-blue-600/30 hover:text-white":"text-blue-700 hover:bg-blue-100 hover:text-blue-800"} transition-colors duration-150`,onClick:()=>mr(O.CustomOpenAI),children:d.jsx("span",{className:"font-medium",children:"OpenAI-compatible API Provider"})})]})})]})]})]}),d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-gray-50"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-left text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"Model Selection"}),d.jsx("div",{className:"space-y-4",children:[Fe.Planner,Fe.Navigator,Fe.Validator].map(c=>d.jsx("div",{children:ie(c)},c))})]}),d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-gray-50"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-left text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"Speech-to-Text Model"}),d.jsx("p",{className:`mb-4 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Configure the Gemini model used for converting speech to text when using the microphone feature."}),d.jsx("div",{className:`rounded-lg border ${s?"border-gray-700 bg-slate-800":"border-gray-200 bg-gray-50"} p-4`,children:d.jsxs("div",{className:"flex items-center",children:[d.jsx("label",{htmlFor:"speech-to-text-model",className:`w-24 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Model"}),d.jsxs("select",{id:"speech-to-text-model",className:`flex-1 rounded-md border text-sm ${s?"border-slate-600 bg-slate-700 text-gray-200":"border-gray-300 bg-white text-gray-700"} px-3 py-2`,value:He,onChange:c=>se(c.target.value),children:[d.jsx("option",{value:"",children:"Choose Model"}),Z.filter(({provider:c,model:v})=>{const m=u[c];return(m==null?void 0:m.type)===O.Gemini}).map(({provider:c,providerName:v,model:m})=>d.jsx("option",{value:`${c}>${m}`,children:`${v} > ${m}`},`${c}>${m}`))]})]})})]})]})},Bm=({isDarkMode:s})=>{const[u,i]=ae.useState(!0),[y,N]=ae.useState([]),[k,E]=ae.useState([]),[P,_]=ae.useState(""),[T,J]=ae.useState("allow"),X=ae.useCallback(async()=>{const R=await cr.getFirewall();i(R.enabled),N(R.allowList),E(R.denyList)},[]);ae.useEffect(()=>{X()},[X]);const Y=async()=>{await cr.updateFirewall({enabled:!u}),await X()},ue=async()=>{const R=P.trim().replace(/^https?:\/\//,"");R&&(T==="allow"?await cr.addToAllowList(R):await cr.addToDenyList(R),await X(),_(""))},ce=async(R,H)=>{H==="allow"?await cr.removeFromAllowList(R):await cr.removeFromDenyList(R),await X()};return d.jsxs("section",{className:"space-y-6",children:[d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-gray-50"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"Firewall"}),d.jsxs("div",{className:"space-y-6",children:[d.jsx("div",{className:`my-6 rounded-lg border p-4 ${s?"border-slate-700 bg-slate-700":"border-gray-200 bg-gray-100"}`,children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("label",{htmlFor:"toggle-firewall",className:`text-base font-medium ${s?"text-gray-200":"text-gray-700"}`,children:"Enable Firewall"}),d.jsxs("div",{className:"relative inline-block w-12 select-none",children:[d.jsx("input",{type:"checkbox",checked:u,onChange:Y,className:"sr-only",id:"toggle-firewall"}),d.jsxs("label",{htmlFor:"toggle-firewall",className:`block h-6 cursor-pointer overflow-hidden rounded-full ${u?"bg-blue-500":s?"bg-gray-600":"bg-gray-300"}`,children:[d.jsx("span",{className:"sr-only",children:"Toggle Firewall"}),d.jsx("span",{className:`block size-6 rounded-full bg-white shadow transition-transform ${u?"translate-x-6":"translate-x-0"}`})]})]})]})}),d.jsx("div",{className:"mb-6 mt-10 flex items-center justify-between",children:d.jsxs("div",{className:"flex space-x-2",children:[d.jsx(Qt,{onClick:()=>J("allow"),className:`px-4 py-2 text-base ${T==="allow"?s?"bg-blue-600 text-white":"bg-blue-500 text-white":s?"bg-slate-700 text-gray-200":"bg-gray-200 text-gray-700"}`,children:"Allow List"}),d.jsx(Qt,{onClick:()=>J("deny"),className:`px-4 py-2 text-base ${T==="deny"?s?"bg-blue-600 text-white":"bg-blue-500 text-white":s?"bg-slate-700 text-gray-200":"bg-gray-200 text-gray-700"}`,children:"Deny List"})]})}),d.jsxs("div",{className:"mb-4 flex space-x-2",children:[d.jsx("input",{id:"url-input",type:"text",value:P,onChange:R=>_(R.target.value),onKeyDown:R=>{R.key==="Enter"&&ue()},placeholder:"Enter domain or URL (e.g. example.com, localhost, 127.0.0.1)",className:`flex-1 rounded-md border px-3 py-2 text-sm ${s?"border-gray-600 bg-slate-700 text-white":"border-gray-300 bg-white text-gray-700"}`}),d.jsx(Qt,{onClick:ue,className:`px-4 py-2 text-sm ${s?"bg-green-600 text-white hover:bg-green-700":"bg-green-500 text-white hover:bg-green-600"}`,children:"Add"})]}),d.jsx("div",{className:"max-h-64 overflow-y-auto",children:T==="allow"?y.length>0?d.jsx("ul",{className:"space-y-2",children:y.map(R=>d.jsxs("li",{className:`flex items-center justify-between rounded-md p-2 pr-0 ${s?"bg-slate-700":"bg-gray-100"}`,children:[d.jsx("span",{className:`text-sm ${s?"text-gray-200":"text-gray-700"}`,children:R}),d.jsx(Qt,{onClick:()=>ce(R,"allow"),className:`rounded-l-none px-2 py-1 text-xs ${s?"bg-red-600 text-white hover:bg-red-700":"bg-red-500 text-white hover:bg-red-600"}`,children:"Remove"})]},R))}):d.jsx("p",{className:`text-center text-sm ${s?"text-gray-400":"text-gray-500"}`,children:"No domains in allow list. Empty allow list means all non-denied domains are allowed."}):k.length>0?d.jsx("ul",{className:"space-y-2",children:k.map(R=>d.jsxs("li",{className:`flex items-center justify-between rounded-md p-2 pr-0 ${s?"bg-slate-700":"bg-gray-100"}`,children:[d.jsx("span",{className:`text-sm ${s?"text-gray-200":"text-gray-700"}`,children:R}),d.jsx(Qt,{onClick:()=>ce(R,"deny"),className:`rounded-l-none px-2 py-1 text-xs ${s?"bg-red-600 text-white hover:bg-red-700":"bg-red-500 text-white hover:bg-red-600"}`,children:"Remove"})]},R))}):d.jsx("p",{className:`text-center text-sm ${s?"text-gray-400":"text-gray-500"}`,children:"No domains in deny list"})})]})]}),d.jsxs("div",{className:`rounded-lg border ${s?"border-slate-700 bg-slate-800":"border-blue-100 bg-gray-50"} p-6 text-left shadow-sm`,children:[d.jsx("h2",{className:`mb-4 text-xl font-semibold ${s?"text-gray-200":"text-gray-800"}`,children:"How the Firewall Works"}),d.jsxs("ul",{className:`list-disc space-y-2 pl-5 text-left text-sm ${s?"text-gray-300":"text-gray-600"}`,children:[d.jsx("li",{children:"The firewall contains a deny list and an allow list."}),d.jsx("li",{children:"If both lists are empty, all URLs are allowed"}),d.jsx("li",{children:"Deny list takes priority - if a URL matches any deny list entry, it's blocked"}),d.jsx("li",{children:"When allow list is empty, all non-denied URLs are allowed"}),d.jsx("li",{className:"font-bold",children:"When allow list is not empty, only matching URLs are allowed"}),d.jsx("li",{children:"Wildcards are NOT supported yet"})]})]})]})},Wm=[{id:"general",icon:"⚙️",label:"General"},{id:"models",icon:"📊",label:"Models"},{id:"firewall",icon:"🔒",label:"Firewall"},{id:"help",icon:"📚",label:"Help"}],Hm=()=>{const[s,u]=ae.useState("models"),[i,y]=ae.useState(!1);ae.useEffect(()=>{const E=window.matchMedia("(prefers-color-scheme: dark)");y(E.matches);const P=_=>{y(_.matches)};return E.addEventListener("change",P),()=>E.removeEventListener("change",P)},[]);const N=E=>{E==="help"?window.open("https://nanobrowser.ai/docs","_blank"):u(E)},k=()=>{switch(s){case"general":return d.jsx(Vm,{isDarkMode:i});case"models":return d.jsx(Dm,{isDarkMode:i});case"firewall":return d.jsx(Bm,{isDarkMode:i});default:return null}};return d.jsxs("div",{className:`flex min-h-screen min-w-[768px] ${i?"bg-slate-900":"bg-[url('/bg.jpg')] bg-cover bg-center"} ${i?"text-gray-200":"text-gray-900"}`,children:[d.jsx("nav",{className:`w-48 border-r ${i?"border-slate-700 bg-slate-800/80":"border-white/20 bg-[#0EA5E9]/10"} backdrop-blur-sm`,children:d.jsxs("div",{className:"p-4",children:[d.jsx("h1",{className:`mb-6 text-xl font-bold ${i?"text-gray-200":"text-gray-800"}`,children:"Settings"}),d.jsx("ul",{className:"space-y-2",children:Wm.map(E=>d.jsx("li",{children:d.jsxs(Qt,{onClick:()=>N(E.id),className:`flex w-full items-center space-x-2 rounded-lg px-4 py-2 text-left text-base 
                    ${s!==E.id?`${i?"bg-slate-700/70 text-gray-300 hover:text-white":"bg-[#0EA5E9]/15 font-medium text-gray-700 hover:text-white"} backdrop-blur-sm`:`${i?"bg-sky-800/50":""} text-white backdrop-blur-sm`}`,children:[d.jsx("span",{children:E.icon}),d.jsx("span",{children:E.label})]})},E.id))})]})}),d.jsx("main",{className:`flex-1 ${i?"bg-slate-800/50":"bg-white/10"} p-8 backdrop-blur-sm`,children:d.jsx("div",{className:"mx-auto min-w-[512px] max-w-screen-lg",children:k()})})]})},Km=am(nm(Hm,d.jsx("div",{children:"Loading..."})),d.jsx("div",{children:"Error Occurred"}));function Qm(){const s=document.querySelector("#app-container");if(!s)throw new Error("Can not find #app-container");const u=dp.createRoot(s);s.className="min-w-[768px]",u.render(d.jsx(Km,{}))}Qm();
